#!/usr/bin/env python3
"""
Complete system setup script for Multi-Strategy Trading System

This script sets up the entire trading system including:
- Basic dependencies installation
- Basic metrics system initialization
- Database setup for long-term data storage
- Grafana dashboard configuration
- System validation and testing
"""

import sys
import subprocess
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def install_dependencies():
    """Install all required dependencies"""
    print("📦 Installing system dependencies...")
    
    required_packages = [
        "numpy>=1.21.0",
        "pandas>=1.3.0", 
        "ccxt>=4.0.0",
        "aiohttp>=3.8.0",
        "cryptography>=3.4.0",
        "pyyaml>=6.0",
        "python-dateutil>=2.8.0"
    ]
    
    for package in required_packages:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ])
            print(f"✅ {package} - installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    # Check sqlite3 (usually included with Python)
    try:
        import sqlite3
        print(f"✅ sqlite3 - available")
    except ImportError:
        print(f"❌ sqlite3 - not available (install Python with sqlite3 support)")
        return False
    
    return True

def setup_basic_metrics():
    """Setup the basic metrics system"""
    print("\n📊 Setting up basic metrics system...")
    
    try:
        # Run the basic metrics setup script
        result = subprocess.run([
            sys.executable, "scripts/setup_basic_metrics.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Basic metrics system setup completed")
            return True
        else:
            print(f"❌ Basic metrics setup failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to run basic metrics setup: {e}")
        return False

def create_config_from_example():
    """Create config.yaml from example if it doesn't exist"""
    print("\n⚙️ Setting up configuration...")
    
    config_path = Path("config.yaml")
    example_path = Path("config.example.yaml")
    
    if config_path.exists():
        print("✅ config.yaml already exists")
        return True
    
    if example_path.exists():
        try:
            import shutil
            shutil.copy2(example_path, config_path)
            print("✅ Created config.yaml from config.example.yaml")
            print("⚠️  Please edit config.yaml with your settings before running the system")
            return True
        except Exception as e:
            print(f"❌ Failed to copy example config: {e}")
            return False
    else:
        print("❌ config.example.yaml not found")
        return False

def validate_system():
    """Validate the complete system setup"""
    print("\n🔍 Validating system setup...")
    
    validation_checks = []
    
    # Check configuration files
    config_files = [
        "config.yaml",
        "config/metrics_config.yaml"
    ]
    
    for config_file in config_files:
        exists = Path(config_file).exists()
        validation_checks.append((f"Config file {config_file}", exists))
    
    # Check directories
    required_dirs = [
        "src",
        "data", 
        "grafana",
        "logs",
        "scripts"
    ]
    
    for directory in required_dirs:
        exists = Path(directory).exists()
        validation_checks.append((f"Directory {directory}", exists))
    
    # Check key source files
    key_files = [
        "main.py",
        "src/core/orchestrator.py",
        "src/portfolio/basic_performance_tracker.py",
        "src/portfolio/basic_metrics_database.py",
        "grafana/basic_dashboard_config.json"
    ]
    
    for key_file in key_files:
        exists = Path(key_file).exists()
        validation_checks.append((f"Key file {key_file}", exists))
    
    # Check database
    try:
        from portfolio.basic_metrics_database import BasicMetricsDatabase
        db = BasicMetricsDatabase("data/basic_portfolio_metrics.db")
        validation_checks.append(("Database connectivity", True))
    except Exception as e:
        validation_checks.append(("Database connectivity", False, str(e)))
    
    # Check imports
    try:
        from config import load_config
        from core.orchestrator import MultiStrategyOrchestrator
        validation_checks.append(("Core imports", True))
    except Exception as e:
        validation_checks.append(("Core imports", False, str(e)))
    
    # Print validation results
    all_passed = True
    for check in validation_checks:
        if len(check) == 2:
            name, passed = check
            status = "✅" if passed else "❌"
            print(f"{status} {name}")
            if not passed:
                all_passed = False
        else:
            name, passed, error = check
            status = "✅" if passed else "❌"
            print(f"{status} {name}")
            if not passed:
                print(f"   Error: {error}")
                all_passed = False
    
    return all_passed

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("🎉 Multi-Strategy Trading System Setup Complete!")
    print("=" * 60)
    
    print("\n📋 Next Steps:")
    print("1. Edit config.yaml with your settings:")
    print("   - Exchange credentials")
    print("   - Capital allocation")
    print("   - Strategy weights")
    print("   - Risk parameters")
    
    print("\n2. Setup Grafana Dashboard:")
    print("   - Install Grafana")
    print("   - Add SQLite data source: data/basic_portfolio_metrics.db")
    print("   - Import dashboard: grafana/basic_dashboard_config.json")
    
    print("\n3. Test the system:")
    print("   - Set simulation_mode: true in config.yaml")
    print("   - Run: python main.py")
    print("   - Check logs for any issues")
    
    print("\n4. Go live:")
    print("   - Set simulation_mode: false")
    print("   - Monitor Grafana dashboard")
    print("   - Check performance metrics regularly")
    
    print("\n📊 Available Metrics (11 Essential):")
    metrics = [
        "PnL Curve - Cumulative arithmetic returns",
        "Sharpe Ratio - Risk-adjusted returns", 
        "Calmar Ratio - Return/max drawdown ratio",
        "Annualized Returns - Arithmetic annual return",
        "Max Drawdown - Peak-to-trough decline",
        "Max Time to Recovery - Days to recover",
        "Portfolio Exposures - Signal strength distribution",
        "Performance Attribution - Long vs short contribution",
        "Turnover - Portfolio turnover rate",
        "Capacity Utilization - Capital deployment efficiency",
        "Slippage - Execution cost analysis"
    ]
    
    for i, metric in enumerate(metrics, 1):
        print(f"   {i:2d}. {metric}")
    
    print("\n📁 Key Files:")
    print("   - config.yaml: Main configuration")
    print("   - main.py: System entry point")
    print("   - data/basic_portfolio_metrics.db: Performance database")
    print("   - logs/: System logs")
    print("   - grafana/: Dashboard configurations")
    
    print("\n🆘 Support:")
    print("   - Check logs/ directory for errors")
    print("   - Review docs/BASIC_METRICS_SETUP.md")
    print("   - Validate setup: python setup_system.py")

def main():
    """Main setup function"""
    print("🚀 Multi-Strategy Trading System Setup")
    print("=" * 50)
    
    steps = [
        ("Installing dependencies", install_dependencies),
        ("Setting up basic metrics", setup_basic_metrics),
        ("Creating configuration", create_config_from_example),
        ("Validating system", validate_system)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        success = step_func()
        
        if not success:
            print(f"❌ Setup failed at step: {step_name}")
            print("\nPlease fix the issues above and run the setup again.")
            return False
    
    print_next_steps()
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
