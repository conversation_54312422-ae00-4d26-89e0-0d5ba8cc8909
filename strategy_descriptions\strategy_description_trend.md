Strategy Name: Trend Trading Strategy
Strategy Type: Time-Series Trend Following

# 1. STRATEGY OVERVIEW
Core Principle: Capture directional price movements on daily timeframes using time-series momentum analysis
Expected Edge: Crypto markets exhibit persistent trends that can be captured through multiple EMA timeframe analysis
Time Horizon: Medium to long-term trend following
Market Conditions: Performs best in strong directional markets; struggles in range-bound or highly volatile conditions
Strategy Classification: Time-series strategy (unlike cross-sectional strategies that rank coins against each other)

# 2. UNIVERSE SELECTION
Selection Criteria:
- Market Cap Range: Top 50 by market cap (using coingecko api)
- Contracts: Perpetual futures only (USDT pairs)
- Volume Threshold: $3M minimum daily volume (30-day average)
- Volatility Filters: Annualized weighted volatility above 5% (to filter out stablecoins)
- Historical Data: Minimum 150 days of close price history required
- Exclusions: Stablecoins, wrapped coins
- Final universe: Up to 50 coins that pass all filters

Implementation Details:
- Data Source: Primary exchange API
- Processing: Batch processing with API rate limiting (20 symbols per batch)
- Refresh Frequency: Weekly universe refresh on Sunday at 00:00 UTC
- Fallback Mechanism: Use previous universe if refresh fails (max 14 days old)

# 3. FEATURE ENGINEERING
Primary Features:
1. **Weighted Volatility**
   - Formula: `weighted_volatility = (0.3 × vol_60d) + (0.5 × vol_30d) + (0.2 × vol_10d)`
   - Annualized volatility calculation (365-day basis for crypto 24/7 trading)
   - Used for signal normalization

2. **Momentum Signals from Multiple EMA Timeframes**
   - Uses log prices for cleaner mathematics
   - EMA pairs: 2/8, 4/16, 8/32, 16/64, 32/128 periods
   - Signal formula for each timeframe: `(short_ema - long_ema) / lookback_volatility`
   - Lookback volatility: Uses volatility of lookback period equal to long EMA period for each pair
   - Equal weight combination: `trend_signal = sum(all_timeframe_signals) / 5`

Feature Validation:
- Data Quality Checks: Minimum 150 days of close price history required
- Error Handling: Skip symbols with calculation failures, use fallback values
- Batch Processing: Process 10 symbols at a time to manage API limits

Key Difference from Cross-Sectional Strategies:
- No beta calculation since this is a time-series strategy (coins evaluated individually)
- No cross-sectional ranking or relative comparison between coins

# 4. POSITION SELECTION
Time-Series Selection Process:
- **Individual Evaluation**: Each coin is evaluated independently against its own historical data
- **No Cross-Sectional Ranking**: Unlike cross-sectional strategies, coins are not ranked against each other
- **All Coins Get Positions**: Every coin in the universe gets evaluated for a position

Position Direction Logic:
- **Positive trend signal** → Long position
- **Negative trend signal** → Short position
- **Zero/neutral trend signal** → No position

This differs from cross-sectional strategies that select only top N performers through relative ranking.

# 5. POSITION SIZING
Sizing Methodology:
1. **Sigmoid Weighting**: Apply `tanh(2×trend_signal)` to each coin's trend signal to convert signals to base weights
2. **Equal Capital Allocation**: Multiply each coin's weights by 1/N for equal capital allocation (for universe of 50 coins, 1/50 = 2%)
3. **Volatility Targeting**: Adjust each coin's weights by `target_volatility / asset_volatility` (target = 20%)
4. **Contract Compliance**: Apply contract specifications and price validation
5. **Beta Projection** (Disabled): Beta projection disabled by default since this is time-series, not cross-sectional

Risk Controls:
- **Leverage Limits**: No leverage limits (volatility targeting provides control)
- **Position Limits**: No maximum position size restrictions
- **Buffer Zones**: 5% tolerance around target positions to prevent over-trading
- **Beta Neutrality**: Optional portfolio beta optimization with max 20% weight change (disabled by default)

Dynamic Adjustments:
- **Rebalancing**: Daily at 00:00 UTC
- **Universe Refresh**: Weekly on Sundays
- **No intraday adjustments**

Key Differences from Cross-Sectional Strategies:
- Uses sigmoid weighting instead of linear decay weights
- Equal capital allocation (1/N) instead of fixed weight allocation
- No cross-sectional ranking considerations


# 6. EXIT RULES
Profit Taking:
- None

Stop Losses:
- None

Rebalancing:
- **Schedule**: Daily rebalancing at 00:00 UTC
- **Execution**: Use existing randomized execution engine with buffer zones
- **Position Closure**: Close all positions not in target list (no dust protection)

# 7. IMPLEMENTATION DETAILS

## Strategy Architecture
- **File Location**: `src/strategies/trend_trading/strategy.py`
- **Configuration**: `src/strategies/trend_trading/config.yaml`
- **Strategy Type**: Time-series (inherits from BaseStrategy)
- **Framework Integration**: Fully integrated with multi-strategy framework

## Key Implementation Features
- **Scalable Design**: Built for reusability with future time-series strategies
- **Efficient Data Processing**: Batch processing with API rate limiting
- **Robust Error Handling**: Comprehensive validation and fallback mechanisms
- **Contract Compliance**: Full integration with exchange contract specifications
- **Performance Monitoring**: Detailed logging and performance tracking

## Data Flow
1. Universe selection with multiple filters
2. Batch OHLCV data fetching with caching
3. Feature calculation (volatility + EMA momentum signals)
4. Time-series position selection (individual evaluation)
5. Sigmoid weighting + equal capital allocation + volatility targeting
6. Contract compliance and price validation
7. Position creation with metadata tracking

# 8. CONFIGURATION PARAMETERS

## Strategy-Specific Parameters
- `top_market_cap_count`: 50 (Top market cap coins to consider)
- `min_daily_volume_usd`: 3,000,000 (Minimum daily volume filter)
- `min_historical_data_days`: 150 (Minimum historical data required)
- `target_volatility`: 0.20 (Target portfolio volatility)
- `sigmoid_multiplier`: 2.0 (Multiplier for sigmoid function)
- `universe_size_for_allocation`: 50 (Expected universe size for equal allocation)
- `ema_timeframes`: [[2,8], [4,16], [8,32], [16,64], [32,128]] (EMA period pairs)
- `buffer_zone_tolerance_percentage`: 5.0 (Position tolerance buffer)

## Shared Framework Parameters
- Uses shared execution, risk management, and monitoring parameters from main config
- Integrates with multi-strategy portfolio combination and performance tracking