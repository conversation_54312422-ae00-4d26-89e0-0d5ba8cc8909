# Enhanced Portfolio Metrics Configuration
# Configuration for comprehensive performance tracking and Grafana integration

# ============================================================================
# METRICS CALCULATION SETTINGS
# ============================================================================

# Risk-free rate for Sharpe ratio calculation (annual)
risk_free_rate: 0.02  # 2% annual risk-free rate

# Calculation frequency
metrics_calculation_frequency_hours: 1  # Calculate metrics every hour
enable_real_time_tracking: true         # Enable real-time metrics calculation

# Data retention
max_performance_history_days: 90        # Keep 90 days of detailed history
max_daily_pnl_history_days: 365        # Keep 1 year of daily PnL data

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# Database settings
metrics_database_path: "data/portfolio_metrics.db"
enable_database_backup: true
backup_frequency_hours: 24
max_backup_files: 7

# Database performance settings
database_connection_timeout: 30
database_query_timeout: 60
enable_database_wal_mode: true  # Write-Ahead Logging for better performance

# ============================================================================
# STRATEGY-SPECIFIC METRICS
# ============================================================================

# Strategy-specific metric configurations
strategy_metrics:
  stat_arb_carry_trade:
    # Funding arbitrage specific metrics
    track_funding_capture_efficiency: true
    track_basis_convergence: true
    track_funding_rate_prediction_accuracy: true
    
    # Custom thresholds
    min_funding_rate_threshold: 0.0001  # 1 bps minimum
    max_position_concentration: 0.3     # 30% max in single position
    
  cross_sectional_momentum:
    # Momentum strategy specific metrics
    track_factor_exposure: true
    track_sector_concentration: true
    track_momentum_persistence: true
    
    # Custom thresholds
    max_sector_concentration: 0.4       # 40% max in single sector
    momentum_decay_threshold: 0.1       # 10% momentum decay threshold
    
  trend_trading:
    # Trend following specific metrics
    track_trend_strength: true
    track_trend_duration: true
    track_whipsaw_ratio: true
    
    # Custom thresholds
    min_trend_strength: 0.3             # 30% minimum trend strength
    max_whipsaw_ratio: 0.2              # 20% max whipsaw trades

# ============================================================================
# PERFORMANCE ATTRIBUTION SETTINGS
# ============================================================================

# Attribution analysis configuration
performance_attribution:
  enable_detailed_attribution: true
  attribution_frequency_hours: 6        # Calculate attribution every 6 hours
  
  # Long/Short attribution
  track_long_short_attribution: true
  track_sector_attribution: true
  track_factor_attribution: true
  
  # Time-based attribution
  track_intraday_attribution: false     # Disable for now (resource intensive)
  track_weekly_attribution: true
  track_monthly_attribution: true

# ============================================================================
# RISK METRICS CONFIGURATION
# ============================================================================

# Risk calculation settings
risk_metrics:
  # VaR/CVaR settings
  var_confidence_levels: [0.95, 0.99]   # 95% and 99% VaR
  var_lookback_days: 252                # 1 year lookback for VaR
  
  # Drawdown analysis
  enable_underwater_curve: true
  track_drawdown_duration: true
  drawdown_threshold: 0.05              # 5% drawdown threshold for alerts
  
  # Volatility metrics
  volatility_lookback_days: 30          # 30-day rolling volatility
  enable_garch_volatility: false        # Disable GARCH for now
  
  # Correlation analysis
  enable_correlation_tracking: true
  correlation_lookback_days: 60         # 60-day rolling correlations

# ============================================================================
# EXECUTION METRICS CONFIGURATION
# ============================================================================

# Execution analysis settings
execution_metrics:
  # Slippage tracking
  track_slippage_by_time: true          # Track slippage by time of day
  track_slippage_by_size: true          # Track slippage by order size
  track_slippage_by_volatility: true    # Track slippage by market volatility
  
  # Market impact
  enable_market_impact_analysis: true
  market_impact_lookback_minutes: 30    # 30-minute market impact window
  
  # Execution timing
  track_execution_latency: true
  track_fill_rates: true
  track_partial_fills: true

# ============================================================================
# GRAFANA INTEGRATION SETTINGS
# ============================================================================

# Grafana dashboard configuration
grafana_integration:
  enable_grafana_export: true
  export_frequency_hours: 1             # Export data every hour
  grafana_export_path: "grafana/data/"
  
  # Dashboard settings
  dashboard_refresh_interval: "1m"      # 1-minute refresh
  default_time_range: "30d"             # 30-day default view
  
  # Panel configurations
  enable_real_time_panels: true
  enable_historical_panels: true
  enable_comparison_panels: true
  
  # Data source settings
  datasource_name: "Portfolio_Metrics_DB"
  datasource_type: "sqlite"
  enable_datasource_caching: true

# ============================================================================
# ALERTING CONFIGURATION
# ============================================================================

# Performance alerts
alerting:
  enable_performance_alerts: true
  
  # Drawdown alerts
  max_drawdown_threshold: 0.15          # 15% max drawdown alert
  max_drawdown_duration_days: 30        # 30-day max drawdown duration
  
  # Performance alerts
  min_sharpe_ratio: 0.5                 # Minimum acceptable Sharpe ratio
  max_volatility: 0.4                   # 40% maximum volatility
  
  # Position alerts
  max_position_concentration: 0.3       # 30% max single position
  max_sector_concentration: 0.4         # 40% max sector concentration
  
  # Execution alerts
  max_slippage_bps: 50                  # 50 bps maximum slippage
  min_fill_rate: 0.8                    # 80% minimum fill rate

# ============================================================================
# EXPORT AND REPORTING SETTINGS
# ============================================================================

# Data export configuration
data_export:
  enable_daily_reports: true
  enable_weekly_reports: true
  enable_monthly_reports: true
  
  # Export formats
  export_formats: ["json", "csv", "parquet"]
  export_path: "reports/"
  
  # Report content
  include_detailed_positions: true
  include_execution_details: true
  include_risk_metrics: true
  include_attribution_analysis: true

# Backup and archival
backup_settings:
  enable_automated_backup: true
  backup_frequency_hours: 24
  backup_retention_days: 90
  backup_path: "backups/"
  
  # Compression settings
  enable_compression: true
  compression_format: "gzip"

# ============================================================================
# PERFORMANCE OPTIMIZATION
# ============================================================================

# Calculation optimization
optimization:
  enable_parallel_calculation: true
  max_worker_threads: 4
  
  # Caching settings
  enable_metrics_caching: true
  cache_ttl_minutes: 15
  max_cache_size_mb: 100
  
  # Database optimization
  enable_database_indexing: true
  enable_query_optimization: true
  batch_insert_size: 1000

# ============================================================================
# LOGGING AND DEBUGGING
# ============================================================================

# Metrics logging
logging:
  metrics_log_level: "INFO"
  enable_performance_logging: true
  enable_calculation_timing: true
  
  # Log file settings
  metrics_log_file: "logs/metrics.log"
  max_log_file_size_mb: 50
  max_log_files: 5
