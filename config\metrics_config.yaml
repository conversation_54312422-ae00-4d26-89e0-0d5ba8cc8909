# Enhanced Portfolio Metrics Configuration - BASIC METRICS ONLY
# Configuration for essential performance tracking and Grafana integration

# ============================================================================
# BASIC METRICS CALCULATION SETTINGS
# ============================================================================

# Risk-free rate for Sharpe ratio calculation (annual)
risk_free_rate: 0.02  # 2% annual risk-free rate

# Calculation frequency
metrics_calculation_frequency_hours: 6  # Calculate metrics every 6 hours (reduced frequency)
enable_real_time_tracking: true         # Enable real-time metrics calculation

# Data retention
max_performance_history_days: 30        # Keep 30 days of detailed history (reduced)
max_daily_pnl_history_days: 90         # Keep 90 days of daily PnL data (reduced)

# ============================================================================
# BASIC METRICS ENABLEMENT (ONLY ESSENTIAL METRICS)
# ============================================================================

# Core performance metrics (ENABLED)
basic_metrics:
  enable_pnl_curve: true                # PnL Curve - Cumulative arithmetic returns
  enable_sharpe_ratio: true             # Sharpe Ratio - Risk-adjusted returns
  enable_calmar_ratio: true             # Calmar Ratio - Return/max drawdown ratio
  enable_annualized_returns: true       # Annualized Returns - Arithmetic annual return
  enable_max_drawdown: true             # Max Drawdown - Peak-to-trough decline
  enable_max_recovery_time: true        # Max Time to Recovery - Days to recover
  enable_portfolio_exposures: true      # Portfolio Ranking & Exposures - Signal strength
  enable_performance_attribution: true  # Performance Attribution - Long vs short
  enable_turnover: true                 # Turnover - Portfolio turnover rate
  enable_capacity_utilization: true     # Capacity Utilization - Capital deployment
  enable_slippage: true                 # Slippage - Execution cost analysis

# Advanced metrics (DISABLED TEMPORARILY)
advanced_metrics:
  enable_var_cvar: false                # VaR/CVaR calculations (disabled)
  enable_correlation_analysis: false    # Correlation matrix (disabled)
  enable_beta_calculation: false        # Market beta calculation (disabled)
  enable_factor_analysis: false         # Factor exposure analysis (disabled)
  enable_sector_analysis: false         # Sector concentration (disabled)
  enable_intraday_metrics: false        # Intraday performance tracking (disabled)

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# Database settings
metrics_database_path: "data/portfolio_metrics.db"
enable_database_backup: true
backup_frequency_hours: 24
max_backup_files: 7

# Database performance settings
database_connection_timeout: 30
database_query_timeout: 60
enable_database_wal_mode: true  # Write-Ahead Logging for better performance

# ============================================================================
# STRATEGY-SPECIFIC METRICS (DISABLED TEMPORARILY)
# ============================================================================

# Strategy-specific metric configurations (ALL DISABLED FOR NOW)
strategy_metrics:
  stat_arb_carry_trade:
    # Funding arbitrage specific metrics (DISABLED)
    track_funding_capture_efficiency: false
    track_basis_convergence: false
    track_funding_rate_prediction_accuracy: false

    # Custom thresholds (basic only)
    min_funding_rate_threshold: 0.0001  # 1 bps minimum
    max_position_concentration: 0.3     # 30% max in single position

  cross_sectional_momentum:
    # Momentum strategy specific metrics (DISABLED)
    track_factor_exposure: false
    track_sector_concentration: false
    track_momentum_persistence: false

    # Custom thresholds (basic only)
    max_sector_concentration: 0.4       # 40% max in single sector
    momentum_decay_threshold: 0.1       # 10% momentum decay threshold

  trend_trading:
    # Trend following specific metrics (DISABLED)
    track_trend_strength: false
    track_trend_duration: false
    track_whipsaw_ratio: false

    # Custom thresholds (basic only)
    min_trend_strength: 0.3             # 30% minimum trend strength
    max_whipsaw_ratio: 0.2              # 20% max whipsaw trades

# ============================================================================
# PERFORMANCE ATTRIBUTION SETTINGS (BASIC ONLY)
# ============================================================================

# Attribution analysis configuration (SIMPLIFIED)
performance_attribution:
  enable_detailed_attribution: false    # Disable detailed attribution for now
  attribution_frequency_hours: 24       # Calculate attribution daily only

  # Long/Short attribution (BASIC ONLY)
  track_long_short_attribution: true    # Keep basic long/short split
  track_sector_attribution: false       # Disable sector attribution
  track_factor_attribution: false       # Disable factor attribution

  # Time-based attribution (DISABLED)
  track_intraday_attribution: false     # Disable intraday
  track_weekly_attribution: false       # Disable weekly
  track_monthly_attribution: false      # Disable monthly

# ============================================================================
# RISK METRICS CONFIGURATION (BASIC ONLY)
# ============================================================================

# Risk calculation settings (SIMPLIFIED)
risk_metrics:
  # VaR/CVaR settings (DISABLED)
  var_confidence_levels: []             # Disable VaR calculations
  var_lookback_days: 0                  # Disable VaR

  # Drawdown analysis (BASIC ONLY)
  enable_underwater_curve: false        # Disable underwater curve
  track_drawdown_duration: true         # Keep basic drawdown duration
  drawdown_threshold: 0.05              # 5% drawdown threshold for alerts

  # Volatility metrics (BASIC ONLY)
  volatility_lookback_days: 30          # 30-day rolling volatility
  enable_garch_volatility: false        # Disable GARCH

  # Correlation analysis (DISABLED)
  enable_correlation_tracking: false    # Disable correlation tracking
  correlation_lookback_days: 0          # Disable correlations

# ============================================================================
# EXECUTION METRICS CONFIGURATION (BASIC ONLY)
# ============================================================================

# Execution analysis settings (SIMPLIFIED)
execution_metrics:
  # Slippage tracking (BASIC ONLY)
  track_slippage_by_time: false         # Disable time-based slippage tracking
  track_slippage_by_size: false         # Disable size-based slippage tracking
  track_slippage_by_volatility: false   # Disable volatility-based slippage tracking

  # Market impact (DISABLED)
  enable_market_impact_analysis: false  # Disable market impact analysis
  market_impact_lookback_minutes: 0     # Disable market impact

  # Execution timing (BASIC ONLY)
  track_execution_latency: false        # Disable latency tracking
  track_fill_rates: true                # Keep basic fill rate tracking
  track_partial_fills: false            # Disable partial fill tracking

# ============================================================================
# GRAFANA INTEGRATION SETTINGS
# ============================================================================

# Grafana dashboard configuration
grafana_integration:
  enable_grafana_export: true
  export_frequency_hours: 1             # Export data every hour
  grafana_export_path: "grafana/data/"
  
  # Dashboard settings
  dashboard_refresh_interval: "1m"      # 1-minute refresh
  default_time_range: "30d"             # 30-day default view
  
  # Panel configurations
  enable_real_time_panels: true
  enable_historical_panels: true
  enable_comparison_panels: true
  
  # Data source settings
  datasource_name: "Portfolio_Metrics_DB"
  datasource_type: "sqlite"
  enable_datasource_caching: true

# ============================================================================
# ALERTING CONFIGURATION
# ============================================================================

# Performance alerts
alerting:
  enable_performance_alerts: true
  
  # Drawdown alerts
  max_drawdown_threshold: 0.15          # 15% max drawdown alert
  max_drawdown_duration_days: 30        # 30-day max drawdown duration
  
  # Performance alerts
  min_sharpe_ratio: 0.5                 # Minimum acceptable Sharpe ratio
  max_volatility: 0.4                   # 40% maximum volatility
  
  # Position alerts
  max_position_concentration: 0.3       # 30% max single position
  max_sector_concentration: 0.4         # 40% max sector concentration
  
  # Execution alerts
  max_slippage_bps: 50                  # 50 bps maximum slippage
  min_fill_rate: 0.8                    # 80% minimum fill rate

# ============================================================================
# EXPORT AND REPORTING SETTINGS
# ============================================================================

# Data export configuration
data_export:
  enable_daily_reports: true
  enable_weekly_reports: true
  enable_monthly_reports: true
  
  # Export formats
  export_formats: ["json", "csv", "parquet"]
  export_path: "reports/"
  
  # Report content
  include_detailed_positions: true
  include_execution_details: true
  include_risk_metrics: true
  include_attribution_analysis: true

# Backup and archival
backup_settings:
  enable_automated_backup: true
  backup_frequency_hours: 24
  backup_retention_days: 90
  backup_path: "backups/"
  
  # Compression settings
  enable_compression: true
  compression_format: "gzip"

# ============================================================================
# PERFORMANCE OPTIMIZATION
# ============================================================================

# Calculation optimization
optimization:
  enable_parallel_calculation: true
  max_worker_threads: 4
  
  # Caching settings
  enable_metrics_caching: true
  cache_ttl_minutes: 15
  max_cache_size_mb: 100
  
  # Database optimization
  enable_database_indexing: true
  enable_query_optimization: true
  batch_insert_size: 1000

# ============================================================================
# LOGGING AND DEBUGGING
# ============================================================================

# Metrics logging
logging:
  metrics_log_level: "INFO"
  enable_performance_logging: true
  enable_calculation_timing: true
  
  # Log file settings
  metrics_log_file: "logs/metrics.log"
  max_log_file_size_mb: 50
  max_log_files: 5
