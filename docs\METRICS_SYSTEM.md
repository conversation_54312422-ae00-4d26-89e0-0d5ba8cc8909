# Enhanced Portfolio Metrics System

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Metrics Tracked](#metrics-tracked)
4. [Database Schema](#database-schema)
5. [Grafana Integration](#grafana-integration)
6. [Configuration](#configuration)
7. [Usage Examples](#usage-examples)
8. [Strategy-Specific Metrics](#strategy-specific-metrics)

## Overview

The Enhanced Portfolio Metrics System provides comprehensive performance tracking and analysis for multi-strategy trading portfolios. It calculates arithmetic returns-based metrics for individual strategies and aggregate portfolios, stores data in SQLite for persistence, and integrates with Grafana for real-time visualization.

### Key Features
- **Comprehensive Metrics**: 15+ performance, risk, and execution metrics
- **Real-time Calculation**: Configurable frequency (default: hourly)
- **Database Storage**: SQLite with optimized schema for time-series data
- **Grafana Integration**: Pre-built dashboards and data source configuration
- **Strategy Attribution**: Detailed performance attribution analysis
- **Arithmetic Returns**: Designed for arithmetic betting strategies

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Strategy      │    │   Enhanced       │    │   Metrics       │
│   Execution     │───▶│   Performance    │───▶│   Database      │
│   Data          │    │   Tracker        │    │   (SQLite)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Metrics        │    │   Grafana       │
                       │   Calculator     │    │   Dashboard     │
                       └──────────────────┘    └─────────────────┘
```

### Components

1. **MetricsCalculator**: Calculates comprehensive performance metrics
2. **MetricsDatabase**: Handles SQLite storage and retrieval
3. **EnhancedPerformanceTracker**: Orchestrates data collection and calculation
4. **Grafana Integration**: Dashboard configuration and data export

## Metrics Tracked

### Core Performance Metrics
1. **PnL Curve**: Cumulative arithmetic returns over time
2. **Sharpe Ratio**: Risk-adjusted returns using arithmetic returns
3. **Calmar Ratio**: Return/max drawdown ratio
4. **Annualized Returns**: Arithmetic annual return
5. **Max Drawdown**: Peak-to-trough decline
6. **Max Time to Recovery**: Days to recover from drawdown

### Risk Metrics
7. **Volatility**: Annualized standard deviation of returns
8. **VaR (95%)**: Value at Risk at 95% confidence
9. **CVaR (95%)**: Conditional Value at Risk

### Performance Attribution
10. **Long vs Short Returns**: Separate performance tracking
11. **Long vs Short Sharpe**: Risk-adjusted performance by side

### Trading Metrics
12. **Turnover Rate**: Portfolio turnover frequency
13. **Capacity Utilization**: Capital deployment efficiency
14. **Average Slippage**: Execution cost in basis points

### Signal Strength
15. **Signal Strength Average**: Mean confidence levels
16. **Signal Strength Std**: Confidence level volatility
17. **Position Concentration**: Herfindahl index of position weights

## Database Schema

### Tables

#### `strategy_metrics`
Stores comprehensive metrics for individual strategies:
```sql
CREATE TABLE strategy_metrics (
    id INTEGER PRIMARY KEY,
    strategy_name TEXT NOT NULL,
    timestamp DATETIME NOT NULL,
    total_return REAL,
    annualized_return REAL,
    sharpe_ratio REAL,
    max_drawdown REAL,
    -- ... additional metrics
);
```

#### `portfolio_metrics`
Stores aggregate portfolio metrics:
```sql
CREATE TABLE portfolio_metrics (
    id INTEGER PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    total_return REAL,
    sharpe_ratio REAL,
    total_strategies INTEGER,
    total_positions INTEGER,
    -- ... additional metrics
);
```

#### `daily_pnl`
Tracks daily PnL for time-series analysis:
```sql
CREATE TABLE daily_pnl (
    id INTEGER PRIMARY KEY,
    date DATE NOT NULL,
    strategy_name TEXT,  -- NULL for aggregate
    daily_return REAL,
    cumulative_return REAL,
    daily_pnl_usd REAL,
    -- ... additional fields
);
```

#### `position_tracking`
Position-level tracking for detailed analysis:
```sql
CREATE TABLE position_tracking (
    id INTEGER PRIMARY KEY,
    date DATE NOT NULL,
    strategy_name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    side TEXT NOT NULL,
    size_usd REAL,
    daily_pnl_usd REAL,
    -- ... additional fields
);
```

#### `execution_tracking`
Execution metrics for slippage analysis:
```sql
CREATE TABLE execution_tracking (
    id INTEGER PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    strategy_name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    slippage_bps REAL,
    execution_time_ms REAL,
    -- ... additional fields
);
```

## Grafana Integration

### Dashboard Panels

1. **Portfolio PnL Curve**: Time-series chart of cumulative returns
2. **Strategy Sharpe Ratios**: Current Sharpe ratios by strategy
3. **Maximum Drawdown**: Drawdown analysis over time
4. **Portfolio Composition**: Pie chart of strategy allocations
5. **Long vs Short Exposure**: Bar gauge of position sides
6. **Key Metrics**: Stats panels for returns, volatility, Calmar ratio
7. **Performance Attribution**: Long/short performance breakdown
8. **Slippage Analysis**: Average slippage by strategy
9. **Capacity Utilization**: Gauge of capital deployment

### Data Source Configuration

```json
{
  "name": "Portfolio_Metrics_DB",
  "type": "sqlite",
  "url": "portfolio_metrics.db",
  "access": "direct"
}
```

### Sample Queries

**Portfolio PnL Curve:**
```sql
SELECT date as time, cumulative_pnl_usd as value 
FROM daily_pnl 
WHERE strategy_name IS NULL 
ORDER BY date
```

**Strategy Performance Comparison:**
```sql
SELECT strategy_name, sharpe_ratio 
FROM strategy_metrics 
WHERE timestamp = (SELECT MAX(timestamp) FROM strategy_metrics)
```

## Configuration

### Main Configuration (`config/metrics_config.yaml`)

```yaml
# Risk-free rate for Sharpe ratio calculation
risk_free_rate: 0.02

# Calculation frequency
metrics_calculation_frequency_hours: 1
enable_real_time_tracking: true

# Database settings
metrics_database_path: "data/portfolio_metrics.db"

# Grafana integration
grafana_integration:
  enable_grafana_export: true
  export_frequency_hours: 1
```

### Strategy-Specific Configuration

```yaml
strategy_metrics:
  stat_arb_carry_trade:
    track_funding_capture_efficiency: true
    track_basis_convergence: true
    
  cross_sectional_momentum:
    track_factor_exposure: true
    track_sector_concentration: true
    
  trend_trading:
    track_trend_strength: true
    track_whipsaw_ratio: true
```

## Usage Examples

### Initialize Enhanced Performance Tracker

```python
from portfolio.enhanced_performance_tracker import EnhancedPerformanceTracker

# Load configuration
config = load_config('config/metrics_config.yaml')

# Initialize tracker
tracker = EnhancedPerformanceTracker(config)
```

### Record Strategy Execution

```python
# Record strategy execution data
await tracker.record_strategy_execution(
    strategy_name="stat_arb_carry_trade",
    strategy_result=strategy_result,
    final_positions=final_positions,
    execution_data=execution_metrics
)
```

### Get Real-time Metrics

```python
# Get current strategy metrics
strategy_metrics = await tracker.calculate_real_time_metrics("stat_arb_carry_trade")

# Get portfolio metrics
portfolio_metrics = await tracker.calculate_real_time_metrics()
```

### Export Grafana Data

```python
# Export data for Grafana
success = await tracker.export_grafana_data("grafana/portfolio_dashboard.json")
```

## Strategy-Specific Metrics

### StatArb Carry Trade
- **Funding Capture Efficiency**: Percentage of available funding captured
- **Basis Convergence**: Rate of basis convergence to expected levels
- **Funding Rate Prediction Accuracy**: Accuracy of funding rate forecasts

### Cross-Sectional Momentum
- **Factor Exposure**: Exposure to momentum factors
- **Sector Concentration**: Concentration across market sectors
- **Momentum Persistence**: Duration of momentum signals

### Trend Trading
- **Trend Strength**: Average strength of trend signals
- **Trend Duration**: Average duration of trend positions
- **Whipsaw Ratio**: Percentage of false breakout trades

## Performance Optimization

### Database Optimization
- Indexed queries for time-series data
- Write-Ahead Logging (WAL) mode
- Batch inserts for efficiency

### Calculation Optimization
- Parallel metric calculation
- Configurable calculation frequency
- In-memory caching for real-time queries

### Memory Management
- Automatic data trimming beyond retention period
- Configurable history limits
- Efficient data structures for time-series

## Monitoring and Alerts

### Performance Alerts
- Maximum drawdown threshold: 15%
- Minimum Sharpe ratio: 0.5
- Maximum volatility: 40%

### Execution Alerts
- Maximum slippage: 50 bps
- Minimum fill rate: 80%

### Position Alerts
- Maximum position concentration: 30%
- Maximum sector concentration: 40%
