"""
Enhanced performance tracker with comprehensive metrics and Grafana integration

This module provides advanced performance tracking capabilities for multi-strategy
portfolios with database storage and real-time metrics calculation.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
from dataclasses import asdict

from .metrics_calculator import MetricsCalculator, PerformanceMetrics
from .metrics_database import MetricsDatabase

logger = logging.getLogger(__name__)


class EnhancedPerformanceTracker:
    """
    Enhanced performance tracker with comprehensive metrics calculation
    
    Provides real-time performance tracking, database storage, and
    Grafana-compatible data export for multi-strategy portfolios.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize enhanced performance tracker
        
        Args:
            config: Configuration containing tracking settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.metrics_calculator = MetricsCalculator(
            risk_free_rate=config.get('risk_free_rate', 0.02)
        )
        self.metrics_db = MetricsDatabase(
            db_path=config.get('metrics_database_path', 'portfolio_metrics.db')
        )
        
        # Configuration
        self.calculation_frequency = config.get('metrics_calculation_frequency_hours', 1)
        self.enable_real_time_tracking = config.get('enable_real_time_tracking', True)
        self.max_history_days = config.get('max_performance_history_days', 90)
        
        # In-memory storage for real-time calculations
        self.strategy_returns: Dict[str, List[float]] = {}
        self.strategy_positions: Dict[str, List[Dict[str, Any]]] = {}
        self.strategy_executions: Dict[str, List[Dict[str, Any]]] = {}
        self.strategy_timestamps: Dict[str, List[datetime]] = {}
        
        # Portfolio-level data
        self.portfolio_returns: List[float] = []
        self.portfolio_positions: List[Dict[str, Any]] = []
        self.portfolio_executions: List[Dict[str, Any]] = []
        self.portfolio_timestamps: List[datetime] = []
        
        # Last calculation timestamps
        self.last_strategy_calculation: Dict[str, datetime] = {}
        self.last_portfolio_calculation: Optional[datetime] = None
        
        self.logger.info("🏗️ Enhanced performance tracker initialized")
        self.logger.info(f"   Calculation frequency: {self.calculation_frequency} hours")
        self.logger.info(f"   Real-time tracking: {'enabled' if self.enable_real_time_tracking else 'disabled'}")
    
    async def record_strategy_execution(self, strategy_name: str, 
                                      strategy_result: Any,
                                      final_positions: List[Any],
                                      execution_data: Optional[Dict[str, Any]] = None) -> None:
        """
        Record strategy execution data for performance tracking
        
        Args:
            strategy_name: Name of the strategy
            strategy_result: StrategyResult object
            final_positions: Final positions for this strategy
            execution_data: Optional execution metrics
        """
        try:
            current_time = datetime.now(timezone.utc)
            
            # Initialize strategy tracking if needed
            if strategy_name not in self.strategy_returns:
                self.strategy_returns[strategy_name] = []
                self.strategy_positions[strategy_name] = []
                self.strategy_executions[strategy_name] = []
                self.strategy_timestamps[strategy_name] = []
            
            # Calculate daily return (placeholder - would need actual PnL calculation)
            daily_return = self._calculate_daily_return(strategy_name, final_positions)
            
            # Store data
            self.strategy_returns[strategy_name].append(daily_return)
            self.strategy_positions[strategy_name].append([
                {
                    'symbol': pos.symbol,
                    'side': pos.side,
                    'size_usd': pos.size_usd,
                    'size_native': pos.size_native,
                    'weight': pos.weight,
                    'confidence': pos.confidence
                }
                for pos in final_positions
            ])
            
            if execution_data:
                self.strategy_executions[strategy_name].append(execution_data)
            
            self.strategy_timestamps[strategy_name].append(current_time)
            
            # Trim old data
            self._trim_strategy_history(strategy_name)
            
            # Calculate and store metrics if enough time has passed
            if self._should_calculate_metrics(strategy_name):
                await self._calculate_and_store_strategy_metrics(strategy_name)
            
            self.logger.debug(f"📊 Recorded execution for strategy: {strategy_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to record strategy execution for {strategy_name}: {e}")
    
    async def record_portfolio_execution(self, combined_positions: List[Any],
                                       strategy_results: Dict[str, Any],
                                       execution_metrics: Dict[str, Any]) -> None:
        """
        Record portfolio-level execution data
        
        Args:
            combined_positions: Final combined portfolio positions
            strategy_results: Results from all strategies
            execution_metrics: Portfolio execution metrics
        """
        try:
            current_time = datetime.now(timezone.utc)
            
            # Calculate portfolio daily return
            portfolio_return = self._calculate_portfolio_daily_return(combined_positions)
            
            # Store portfolio data
            self.portfolio_returns.append(portfolio_return)
            self.portfolio_positions.append([
                {
                    'symbol': pos.symbol,
                    'side': pos.side,
                    'size_usd': pos.size_usd,
                    'contributing_strategies': pos.contributing_strategies,
                    'strategy_contributions': pos.strategy_contributions
                }
                for pos in combined_positions
            ])
            self.portfolio_executions.append(execution_metrics)
            self.portfolio_timestamps.append(current_time)
            
            # Trim old data
            self._trim_portfolio_history()
            
            # Calculate and store portfolio metrics
            if self._should_calculate_portfolio_metrics():
                await self._calculate_and_store_portfolio_metrics()
            
            self.logger.debug("📊 Recorded portfolio execution")
            
        except Exception as e:
            self.logger.error(f"Failed to record portfolio execution: {e}")
    
    async def calculate_real_time_metrics(self, strategy_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Calculate real-time performance metrics
        
        Args:
            strategy_name: Strategy name (None for portfolio)
            
        Returns:
            Dictionary containing current metrics
        """
        try:
            if strategy_name:
                return await self._calculate_strategy_metrics_dict(strategy_name)
            else:
                return await self._calculate_portfolio_metrics_dict()
                
        except Exception as e:
            self.logger.error(f"Failed to calculate real-time metrics: {e}")
            return {}
    
    async def get_performance_summary(self, days: int = 30) -> Dict[str, Any]:
        """
        Get comprehensive performance summary
        
        Args:
            days: Number of days to include
            
        Returns:
            Performance summary dictionary
        """
        try:
            summary = {
                'portfolio': {},
                'strategies': {},
                'generation_time': datetime.now(timezone.utc).isoformat()
            }
            
            # Get portfolio metrics from database
            portfolio_history = self.metrics_db.get_portfolio_metrics_history(days)
            if portfolio_history:
                latest_portfolio = portfolio_history[0]
                summary['portfolio'] = {
                    'latest_metrics': latest_portfolio,
                    'history_count': len(portfolio_history)
                }
            
            # Get strategy metrics from database
            for strategy_name in self.strategy_returns.keys():
                strategy_history = self.metrics_db.get_strategy_metrics_history(strategy_name, days)
                if strategy_history:
                    summary['strategies'][strategy_name] = {
                        'latest_metrics': strategy_history[0],
                        'history_count': len(strategy_history)
                    }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to get performance summary: {e}")
            return {}
    
    def _calculate_daily_return(self, strategy_name: str, positions: List[Any]) -> float:
        """
        Calculate daily return for a strategy (placeholder implementation)
        
        In a real implementation, this would calculate actual PnL based on
        position changes and market movements.
        """
        # Placeholder: return random value for demonstration
        # In practice, this would calculate actual PnL
        import random
        return random.uniform(-0.02, 0.02)  # ±2% daily return
    
    def _calculate_portfolio_daily_return(self, positions: List[Any]) -> float:
        """
        Calculate daily return for the portfolio (placeholder implementation)
        """
        # Placeholder: return random value for demonstration
        import random
        return random.uniform(-0.015, 0.015)  # ±1.5% daily return
    
    def _should_calculate_metrics(self, strategy_name: str) -> bool:
        """Check if enough time has passed to recalculate metrics"""
        if not self.enable_real_time_tracking:
            return False
        
        last_calc = self.last_strategy_calculation.get(strategy_name)
        if not last_calc:
            return True
        
        time_diff = datetime.now(timezone.utc) - last_calc
        return time_diff.total_seconds() >= (self.calculation_frequency * 3600)
    
    def _should_calculate_portfolio_metrics(self) -> bool:
        """Check if enough time has passed to recalculate portfolio metrics"""
        if not self.enable_real_time_tracking:
            return False
        
        if not self.last_portfolio_calculation:
            return True
        
        time_diff = datetime.now(timezone.utc) - self.last_portfolio_calculation
        return time_diff.total_seconds() >= (self.calculation_frequency * 3600)
    
    def _trim_strategy_history(self, strategy_name: str) -> None:
        """Trim old strategy data beyond retention period"""
        max_records = self.max_history_days * 24  # Assuming hourly records
        
        if len(self.strategy_returns[strategy_name]) > max_records:
            self.strategy_returns[strategy_name] = self.strategy_returns[strategy_name][-max_records:]
            self.strategy_positions[strategy_name] = self.strategy_positions[strategy_name][-max_records:]
            self.strategy_executions[strategy_name] = self.strategy_executions[strategy_name][-max_records:]
            self.strategy_timestamps[strategy_name] = self.strategy_timestamps[strategy_name][-max_records:]
    
    def _trim_portfolio_history(self) -> None:
        """Trim old portfolio data beyond retention period"""
        max_records = self.max_history_days * 24  # Assuming hourly records
        
        if len(self.portfolio_returns) > max_records:
            self.portfolio_returns = self.portfolio_returns[-max_records:]
            self.portfolio_positions = self.portfolio_positions[-max_records:]
            self.portfolio_executions = self.portfolio_executions[-max_records:]
            self.portfolio_timestamps = self.portfolio_timestamps[-max_records:]

    async def _calculate_and_store_strategy_metrics(self, strategy_name: str) -> None:
        """Calculate and store strategy metrics in database"""
        try:
            if strategy_name not in self.strategy_returns or not self.strategy_returns[strategy_name]:
                return

            # Calculate metrics
            metrics = self.metrics_calculator.calculate_strategy_metrics(
                returns_series=self.strategy_returns[strategy_name],
                positions_history=self.strategy_positions[strategy_name],
                execution_history=self.strategy_executions[strategy_name],
                timestamps=self.strategy_timestamps[strategy_name]
            )

            # Store in database
            success = self.metrics_db.store_strategy_metrics(strategy_name, metrics)

            if success:
                self.last_strategy_calculation[strategy_name] = datetime.now(timezone.utc)
                self.logger.debug(f"📊 Calculated and stored metrics for {strategy_name}")

        except Exception as e:
            self.logger.error(f"Failed to calculate strategy metrics for {strategy_name}: {e}")

    async def _calculate_and_store_portfolio_metrics(self) -> None:
        """Calculate and store portfolio metrics in database"""
        try:
            if not self.portfolio_returns:
                return

            # Calculate portfolio metrics
            metrics = self.metrics_calculator.calculate_strategy_metrics(
                returns_series=self.portfolio_returns,
                positions_history=self.portfolio_positions,
                execution_history=self.portfolio_executions,
                timestamps=self.portfolio_timestamps
            )

            # Prepare portfolio statistics
            latest_positions = self.portfolio_positions[-1] if self.portfolio_positions else []
            portfolio_stats = {
                'total_strategies': len(self.strategy_returns),
                'total_positions': len(latest_positions),
                'long_positions': len([p for p in latest_positions if p.get('side') == 'long']),
                'short_positions': len([p for p in latest_positions if p.get('side') == 'short']),
                'total_capital': sum(p.get('size_usd', 0) for p in latest_positions),
                'long_capital': sum(p.get('size_usd', 0) for p in latest_positions if p.get('side') == 'long'),
                'short_capital': sum(p.get('size_usd', 0) for p in latest_positions if p.get('side') == 'short')
            }

            # Store in database
            success = self.metrics_db.store_portfolio_metrics(metrics, portfolio_stats)

            if success:
                self.last_portfolio_calculation = datetime.now(timezone.utc)
                self.logger.debug("📊 Calculated and stored portfolio metrics")

        except Exception as e:
            self.logger.error(f"Failed to calculate portfolio metrics: {e}")

    async def _calculate_strategy_metrics_dict(self, strategy_name: str) -> Dict[str, Any]:
        """Calculate current strategy metrics and return as dictionary"""
        try:
            if strategy_name not in self.strategy_returns or not self.strategy_returns[strategy_name]:
                return {}

            metrics = self.metrics_calculator.calculate_strategy_metrics(
                returns_series=self.strategy_returns[strategy_name],
                positions_history=self.strategy_positions[strategy_name],
                execution_history=self.strategy_executions[strategy_name],
                timestamps=self.strategy_timestamps[strategy_name]
            )

            return metrics.to_dict()

        except Exception as e:
            self.logger.error(f"Failed to calculate strategy metrics dict for {strategy_name}: {e}")
            return {}

    async def _calculate_portfolio_metrics_dict(self) -> Dict[str, Any]:
        """Calculate current portfolio metrics and return as dictionary"""
        try:
            if not self.portfolio_returns:
                return {}

            metrics = self.metrics_calculator.calculate_strategy_metrics(
                returns_series=self.portfolio_returns,
                positions_history=self.portfolio_positions,
                execution_history=self.portfolio_executions,
                timestamps=self.portfolio_timestamps
            )

            return metrics.to_dict()

        except Exception as e:
            self.logger.error(f"Failed to calculate portfolio metrics dict: {e}")
            return {}

    async def export_grafana_data(self, output_path: str = "grafana_export.json") -> bool:
        """
        Export data in Grafana-compatible format

        Args:
            output_path: Path to export file

        Returns:
            True if successful, False otherwise
        """
        try:
            import json
            from pathlib import Path

            # Prepare Grafana-compatible data structure
            grafana_data = {
                'dashboard': {
                    'title': 'Multi-Strategy Portfolio Performance',
                    'tags': ['trading', 'portfolio', 'performance'],
                    'timezone': 'UTC',
                    'refresh': '1m',
                    'time': {
                        'from': 'now-30d',
                        'to': 'now'
                    }
                },
                'datasources': [
                    {
                        'name': 'Portfolio Metrics DB',
                        'type': 'sqlite',
                        'url': str(self.metrics_db.db_path),
                        'access': 'direct'
                    }
                ],
                'panels': self._generate_grafana_panels()
            }

            # Export to file
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w') as f:
                json.dump(grafana_data, f, indent=2, default=str)

            self.logger.info(f"📤 Exported Grafana data to {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export Grafana data: {e}")
            return False

    def _generate_grafana_panels(self) -> List[Dict[str, Any]]:
        """Generate Grafana panel configurations"""
        panels = []

        # Panel 1: Portfolio PnL Curve
        panels.append({
            'title': 'Portfolio PnL Curve',
            'type': 'graph',
            'targets': [{
                'rawSql': """
                    SELECT date as time, cumulative_pnl_usd as value, 'Portfolio' as metric
                    FROM daily_pnl
                    WHERE strategy_name IS NULL
                    ORDER BY date
                """,
                'format': 'time_series'
            }],
            'yAxes': [{'label': 'PnL (USD)'}],
            'gridPos': {'h': 8, 'w': 12, 'x': 0, 'y': 0}
        })

        # Panel 2: Strategy Performance Comparison
        panels.append({
            'title': 'Strategy Sharpe Ratios',
            'type': 'stat',
            'targets': [{
                'rawSql': """
                    SELECT strategy_name, sharpe_ratio
                    FROM strategy_metrics
                    WHERE timestamp = (SELECT MAX(timestamp) FROM strategy_metrics)
                """,
                'format': 'table'
            }],
            'gridPos': {'h': 8, 'w': 12, 'x': 12, 'y': 0}
        })

        # Panel 3: Drawdown Analysis
        panels.append({
            'title': 'Maximum Drawdown',
            'type': 'graph',
            'targets': [{
                'rawSql': """
                    SELECT timestamp as time, max_drawdown as value, strategy_name as metric
                    FROM strategy_metrics
                    ORDER BY timestamp
                """,
                'format': 'time_series'
            }],
            'yAxes': [{'label': 'Drawdown (%)'}],
            'gridPos': {'h': 8, 'w': 24, 'x': 0, 'y': 8}
        })

        return panels
