{"dashboard": {"id": null, "title": "Multi-Strategy Portfolio Performance", "tags": ["trading", "portfolio", "performance", "multi-strategy"], "style": "dark", "timezone": "UTC", "refresh": "1m", "time": {"from": "now-30d", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "panels": [{"id": 1, "title": "Portfolio PnL Curve", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"rawSql": "SELECT date as time, cumulative_pnl_usd as value FROM daily_pnl WHERE strategy_name IS NULL ORDER BY date", "format": "time_series", "refId": "A"}], "yAxes": [{"label": "PnL (USD)", "show": true}], "xAxis": {"show": true, "mode": "time"}, "legend": {"show": true, "values": false, "min": false, "max": false, "current": false, "total": false, "avg": false}}, {"id": 2, "title": "Strategy <PERSON>", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"rawSql": "SELECT strategy_name, sharpe_ratio FROM strategy_metrics WHERE timestamp = (SELECT MAX(timestamp) FROM strategy_metrics)", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 1.0}]}}}}, {"id": 3, "title": "Maximum Drawdown by Strategy", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "targets": [{"rawSql": "SELECT timestamp as time, max_drawdown as value, strategy_name as metric FROM strategy_metrics ORDER BY timestamp", "format": "time_series", "refId": "A"}], "yAxes": [{"label": "Drawdown (%)", "show": true, "max": 0}]}, {"id": 4, "title": "Portfolio Composition", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"rawSql": "SELECT strategy_name, SUM(size_usd) as value FROM position_tracking WHERE date = (SELECT MAX(date) FROM position_tracking) GROUP BY strategy_name", "format": "table", "refId": "A"}]}, {"id": 5, "title": "Long vs Short Exposure", "type": "bargauge", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"rawSql": "SELECT side, SUM(size_usd) as value FROM position_tracking WHERE date = (SELECT MAX(date) FROM position_tracking) GROUP BY side", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}}, {"id": 6, "title": "Annualized Returns", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}, "targets": [{"rawSql": "SELECT annualized_return FROM portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "decimals": 2}}}, {"id": 7, "title": "Portfolio Volatility", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 24}, "targets": [{"rawSql": "SELECT volatility FROM portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "decimals": 2}}}, {"id": 8, "title": "Cal<PERSON>", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 24}, "targets": [{"rawSql": "SELECT calmar_ratio FROM portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"decimals": 2}}}, {"id": 9, "title": "Portfolio Turnover", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 24}, "targets": [{"rawSql": "SELECT turnover_rate FROM portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "decimals": 2}}}, {"id": 10, "title": "Strategy Performance Attribution", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "targets": [{"rawSql": "SELECT timestamp as time, long_return as 'Long Return', short_return as 'Short Return', strategy_name FROM strategy_metrics ORDER BY timestamp", "format": "time_series", "refId": "A"}], "yAxes": [{"label": "Return (%)", "show": true}]}, {"id": 11, "title": "Average Slippage by Strategy", "type": "bargauge", "gridPos": {"h": 6, "w": 12, "x": 0, "y": 36}, "targets": [{"rawSql": "SELECT strategy_name, AVG(avg_slippage_bps) as avg_slippage FROM strategy_metrics WHERE timestamp >= datetime('now', '-7 days') GROUP BY strategy_name", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "decimals": 1}}}, {"id": 12, "title": "Capacity Utilization", "type": "gauge", "gridPos": {"h": 6, "w": 12, "x": 12, "y": 36}, "targets": [{"rawSql": "SELECT capacity_utilization FROM portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 0.8}]}}}}]}}