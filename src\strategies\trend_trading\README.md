# Trend Trading Strategy

A time-series trend following strategy that captures directional price movements on daily timeframes by taking positions in all coins based on their individual momentum signals using multiple EMA timeframes.

## Table of Contents

1. [Strategy Overview](#strategy-overview)
2. [Universe Selection](#universe-selection)
3. [Feature Engineering](#feature-engineering)
4. [Model](#model)
5. [Position Selection](#position-selection)
6. [Position Sizing](#position-sizing)
7. [Target Portfolio Generation](#target-portfolio-generation)
8. [Configuration Parameters](#configuration-parameters)
9. [Risk Management](#risk-management)
10. [Performance Characteristics](#performance-characteristics)

## Strategy Overview

**Core Principle**: Capture directional price movements on daily timeframes using time-series momentum analysis.

**Expected Edge**: Crypto markets exhibit persistent trends that can be captured through multiple EMA timeframe analysis.

**Time Horizon**: Medium to long-term trend following.

**Market Conditions**: Performs best in strong directional markets; struggles in range-bound or highly volatile conditions.

**Strategy Type**: Time-series strategy (unlike cross-sectional strategies that rank coins against each other).

## Universe Selection

The strategy selects a universe of cryptocurrency perpetual futures based on:

### Selection Criteria
- **Market Cap Range**: Top 50 by market cap (using CoinGecko API)
- **Contracts**: Perpetual futures only (USDT pairs)
- **Volume Threshold**: $3M minimum daily volume (30-day average)
- **Volatility Filters**: Annualized weighted volatility above 5% (to filter out stablecoins)
- **Historical Data**: Minimum 150 days of close price history required
- **Exclusions**: Stablecoins, wrapped coins
- **Final Universe**: Up to 50 coins that pass all filters

### Implementation Details
- **Data Source**: Primary exchange API
- **Processing**: Batch processing with API rate limiting (10 symbols per batch)
- **Refresh Frequency**: Weekly universe refresh on Sunday at 00:00 UTC
- **Fallback Mechanism**: Use previous universe if refresh fails (max 14 days old)

## Feature Engineering

### Primary Features

1. **Weighted Volatility**
   - Formula: `weighted_volatility = (0.3 × vol_60d) + (0.5 × vol_30d) + (0.2 × vol_10d)`
   - Annualized volatility calculation (365-day basis for crypto 24/7 trading)
   - Used for signal normalization

2. **Momentum Signals from Multiple EMA Timeframes**
   - Uses log prices for cleaner mathematics
   - EMA pairs: 2/8, 4/16, 8/32, 16/64, 32/128 periods
   - Signal formula for each timeframe: `(short_ema - long_ema) / lookback_volatility`
   - Lookback volatility: Uses volatility of lookback period equal to long EMA period for each pair
   - Equal weight combination: `trend_signal = sum(all_timeframe_signals) / 5`

### Feature Validation
- **Data Quality Checks**: Minimum 150 days of close price history required
- **Error Handling**: Skip symbols with calculation failures, use fallback values
- **Batch Processing**: Process 10 symbols at a time to manage API limits

## Model

**Model Type**: Time-series momentum model using multiple EMA crossovers.

**Signal Generation**: Each coin evaluated individually against its own historical data (not cross-sectionally ranked).

**Signal Combination**: Equal-weighted average of 5 different EMA timeframe signals.

## Position Selection

### Time-Series Approach
- **Individual Evaluation**: Each coin is evaluated independently against its own historical data
- **No Cross-Sectional Ranking**: Unlike cross-sectional strategies, coins are not ranked against each other
- **All Coins Get Positions**: Every coin in the universe gets evaluated for a position

### Position Direction Logic
- **Positive trend signal** → Long position
- **Negative trend signal** → Short position  
- **Zero/neutral trend signal** → No position

This differs from cross-sectional strategies that select only top N performers through relative ranking.

## Position Sizing

### Sizing Methodology

1. **Sigmoid Weighting**: Apply `tanh(2×trend_signal)` to each coin's trend signal to convert signals to base weights
2. **Equal Capital Allocation**: Multiply each coin's weights by 1/N for equal capital allocation (for universe of 50 coins, 1/50 = 2%)
3. **Volatility Targeting**: Adjust each coin's weights by `target_volatility / asset_volatility` (target = 20%)
4. **Contract Compliance**: Apply contract specifications and price validation

### Risk Controls
- **Leverage Limits**: No leverage limits (volatility targeting provides control)
- **Position Limits**: No maximum position size restrictions
- **Buffer Zones**: 5% tolerance around target positions to prevent over-trading
- **Beta Projection**: Disabled by default (since this is time-series, not cross-sectional)

### Dynamic Adjustments
- **Rebalancing**: Daily at 00:00 UTC
- **Universe Refresh**: Weekly on Sundays
- **No intraday adjustments**

## Target Portfolio Generation

The strategy generates a target portfolio where:

1. **Position Count**: Variable (depends on how many coins have non-zero trend signals)
2. **Capital Allocation**: Equal base allocation (1/N) modified by sigmoid weighting and volatility targeting
3. **Long/Short Balance**: Determined by individual coin signals, not forced balance
4. **Position Sizes**: Calculated with contract compliance and price validation

## Configuration Parameters

Key configurable parameters include:

- `top_market_cap_count`: Number of top market cap coins to consider (default: 50)
- `min_daily_volume_usd`: Minimum daily volume filter (default: $3M)
- `min_historical_data_days`: Minimum historical data required (default: 150 days)
- `target_volatility`: Target portfolio volatility (default: 20%)
- `sigmoid_multiplier`: Multiplier for sigmoid function (default: 2.0)
- `ema_timeframes`: EMA period pairs for momentum calculation
- `buffer_zone_tolerance_percentage`: Position tolerance buffer (default: 5%)

## Risk Management

### Portfolio-Level Controls
- **Volatility Targeting**: Adjusts position sizes to target 20% portfolio volatility
- **Buffer Zones**: 5% tolerance around target positions to prevent over-trading
- **Position Closure**: Close all positions not in target list (no dust protection)

### Position-Level Controls
- **Contract Compliance**: All positions validated against exchange contract specifications
- **Price Validation**: Current prices validated before position sizing
- **Minimum Volatility**: Exclude assets with volatility below 5% threshold

### Execution Controls
- **Conservative Execution**: Post-only orders with randomized timing
- **Rate Limiting**: Conservative API usage to avoid exchange limits
- **Error Handling**: Robust error recovery and fallback mechanisms

## Performance Characteristics

### Expected Performance
- **Market Conditions**: Best performance in trending markets
- **Volatility**: Moderate volatility through volatility targeting
- **Drawdowns**: Potential for significant drawdowns in range-bound markets
- **Correlation**: Low correlation with cross-sectional strategies

### Monitoring Metrics
- **Trend Signal Distribution**: Monitor distribution of trend signals across universe
- **Position Count**: Track number of long vs short positions
- **Capital Utilization**: Monitor percentage of capital deployed
- **Volatility Realization**: Compare realized vs target volatility
