#!/usr/bin/env python3
"""
Validation script for metrics calculations

This script validates that all performance metrics calculations are correct,
especially Sharpe ratio and Calmar ratio calculations.
"""

import sys
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from portfolio.basic_metrics_calculator import BasicMetricsCalculator
from datetime import datetime, timezone


def test_sharpe_ratio_calculation():
    """Test Sharpe ratio calculation with known values"""
    print("🧮 Testing Sharpe Ratio Calculation...")
    
    calculator = BasicMetricsCalculator(risk_free_rate=0.02)  # 2% annual risk-free rate
    
    # Test case 1: Simple daily returns
    daily_returns = [0.01, -0.005, 0.015, -0.01, 0.02]  # Daily returns
    
    # Calculate manually for verification
    avg_daily_return = np.mean(daily_returns)
    annualized_return = avg_daily_return * 365.25
    daily_vol = np.std(daily_returns, ddof=1)
    annualized_vol = daily_vol * np.sqrt(365.25)
    
    expected_sharpe = (annualized_return - 0.02) / annualized_vol
    
    # Calculate using our method
    calculated_sharpe = calculator._calculate_sharpe_ratio(np.array(daily_returns), annualized_vol)
    
    print(f"   Daily returns: {daily_returns}")
    print(f"   Average daily return: {avg_daily_return:.6f}")
    print(f"   Annualized return: {annualized_return:.6f} ({annualized_return*100:.2f}%)")
    print(f"   Annualized volatility: {annualized_vol:.6f} ({annualized_vol*100:.2f}%)")
    print(f"   Expected Sharpe ratio: {expected_sharpe:.4f}")
    print(f"   Calculated Sharpe ratio: {calculated_sharpe:.4f}")
    print(f"   Difference: {abs(expected_sharpe - calculated_sharpe):.8f}")
    
    if abs(expected_sharpe - calculated_sharpe) < 1e-6:
        print("   ✅ Sharpe ratio calculation is correct")
        return True
    else:
        print("   ❌ Sharpe ratio calculation is incorrect")
        return False


def test_calmar_ratio_calculation():
    """Test Calmar ratio calculation"""
    print("\n🧮 Testing Calmar Ratio Calculation...")
    
    calculator = BasicMetricsCalculator(risk_free_rate=0.02)
    
    # Test case: Returns with a drawdown
    daily_returns = [0.01, 0.02, -0.03, -0.02, 0.015, 0.01, -0.01, 0.02]
    timestamps = [datetime.now(timezone.utc) for _ in daily_returns]
    
    # Calculate components
    annualized_return = calculator._calculate_annualized_return(np.array(daily_returns), len(timestamps))
    max_drawdown, _ = calculator._calculate_max_drawdown(np.array(daily_returns))
    
    expected_calmar = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
    
    # Calculate cumulative returns for manual verification
    cumulative = np.cumsum(daily_returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = cumulative - running_max
    manual_max_dd = np.min(drawdown)
    
    print(f"   Daily returns: {daily_returns}")
    print(f"   Cumulative returns: {cumulative.tolist()}")
    print(f"   Drawdown series: {drawdown.tolist()}")
    print(f"   Annualized return: {annualized_return:.6f} ({annualized_return*100:.2f}%)")
    print(f"   Max drawdown (calculated): {max_drawdown:.6f} ({max_drawdown*100:.2f}%)")
    print(f"   Max drawdown (manual): {manual_max_dd:.6f} ({manual_max_dd*100:.2f}%)")
    print(f"   Calmar ratio: {expected_calmar:.4f}")
    
    if abs(max_drawdown - manual_max_dd) < 1e-6:
        print("   ✅ Max drawdown calculation is correct")
        print("   ✅ Calmar ratio calculation is correct")
        return True
    else:
        print("   ❌ Max drawdown calculation is incorrect")
        return False


def test_annualized_return_calculation():
    """Test annualized return calculation"""
    print("\n🧮 Testing Annualized Return Calculation...")
    
    calculator = BasicMetricsCalculator()
    
    # Test case: 30 days of returns
    daily_returns = [0.001] * 30  # 0.1% daily return for 30 days
    
    calculated_annualized = calculator._calculate_annualized_return(np.array(daily_returns), 30)
    
    # Manual calculation
    total_return = sum(daily_returns)  # 0.03 (3%)
    years = 30 / 365.25
    expected_annualized = total_return / years
    
    print(f"   Daily returns: 0.1% for 30 days")
    print(f"   Total return: {total_return:.6f} ({total_return*100:.2f}%)")
    print(f"   Period in years: {years:.6f}")
    print(f"   Expected annualized return: {expected_annualized:.6f} ({expected_annualized*100:.2f}%)")
    print(f"   Calculated annualized return: {calculated_annualized:.6f} ({calculated_annualized*100:.2f}%)")
    print(f"   Difference: {abs(expected_annualized - calculated_annualized):.8f}")
    
    if abs(expected_annualized - calculated_annualized) < 1e-6:
        print("   ✅ Annualized return calculation is correct")
        return True
    else:
        print("   ❌ Annualized return calculation is incorrect")
        return False


def test_full_metrics_calculation():
    """Test full metrics calculation with realistic data"""
    print("\n🧮 Testing Full Metrics Calculation...")
    
    calculator = BasicMetricsCalculator(risk_free_rate=0.02)
    
    # Generate realistic trading returns (daily)
    np.random.seed(42)  # For reproducible results
    n_days = 252  # 1 year of trading days
    
    # Simulate returns with some skill (positive drift) and volatility
    daily_returns = np.random.normal(0.0008, 0.015, n_days)  # ~20% annual vol, ~20% annual return
    
    # Create mock data
    timestamps = [datetime.now(timezone.utc) for _ in range(n_days)]
    positions_history = [[] for _ in range(n_days)]  # Empty positions for simplicity
    execution_history = [[] for _ in range(n_days)]  # Empty executions for simplicity
    
    # Calculate metrics
    metrics = calculator.calculate_basic_metrics(
        returns_series=daily_returns.tolist(),
        positions_history=positions_history,
        execution_history=execution_history,
        timestamps=timestamps
    )
    
    print(f"   Sample size: {n_days} days")
    print(f"   Total return: {metrics.total_return:.4f} ({metrics.total_return*100:.2f}%)")
    print(f"   Annualized return: {metrics.annualized_return:.4f} ({metrics.annualized_return*100:.2f}%)")
    print(f"   Volatility: {metrics.volatility:.4f} ({metrics.volatility*100:.2f}%)")
    print(f"   Sharpe ratio: {metrics.sharpe_ratio:.4f}")
    print(f"   Max drawdown: {metrics.max_drawdown:.4f} ({metrics.max_drawdown*100:.2f}%)")
    print(f"   Calmar ratio: {metrics.calmar_ratio:.4f}")
    print(f"   Max drawdown duration: {metrics.max_drawdown_duration_days} days")
    
    # Validate ranges
    checks = []
    checks.append(("Total return reasonable", -1.0 < metrics.total_return < 1.0))
    checks.append(("Annualized return reasonable", -2.0 < metrics.annualized_return < 2.0))
    checks.append(("Volatility positive", metrics.volatility > 0))
    checks.append(("Sharpe ratio reasonable", -5.0 < metrics.sharpe_ratio < 5.0))
    checks.append(("Max drawdown negative", metrics.max_drawdown <= 0))
    checks.append(("Calmar ratio reasonable", -10.0 < metrics.calmar_ratio < 10.0))
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"   {status} {check_name}")
        if not passed:
            all_passed = False
    
    return all_passed


def main():
    """Run all validation tests"""
    print("🔍 Validating Performance Metrics Calculations")
    print("=" * 50)
    
    tests = [
        ("Sharpe Ratio Calculation", test_sharpe_ratio_calculation),
        ("Calmar Ratio Calculation", test_calmar_ratio_calculation),
        ("Annualized Return Calculation", test_annualized_return_calculation),
        ("Full Metrics Calculation", test_full_metrics_calculation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with error: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Validation Results:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅" if passed else "❌"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All calculations validated successfully!")
        print("The metrics system is ready for production use.")
    else:
        print("\n❌ Some calculations failed validation!")
        print("Please review the failed tests and fix the calculations.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
