"""
Basic database storage for essential portfolio metrics

This module handles SQLite storage for basic performance metrics only,
focusing on essential metrics for Grafana integration.
"""

import logging
import sqlite3
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from pathlib import Path
from contextlib import contextmanager

from .basic_metrics_calculator import BasicPerformanceMetrics

logger = logging.getLogger(__name__)


class BasicMetricsDatabase:
    """
    Basic database manager for essential portfolio metrics storage
    
    Handles SQLite database operations for storing and retrieving
    basic performance metrics for Grafana dashboard integration.
    """
    
    def __init__(self, db_path: str = "basic_portfolio_metrics.db"):
        """
        Initialize basic metrics database
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
        
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database schema
        self._initialize_basic_database()
        
        self.logger.info(f"📊 Basic metrics database initialized: {self.db_path}")
    
    def _initialize_basic_database(self):
        """Create basic database tables if they don't exist"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Basic strategy performance metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS basic_strategy_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    period_start DATETIME,
                    period_end DATETIME,
                    
                    -- Core performance metrics
                    total_return REAL,
                    annualized_return REAL,
                    volatility REAL,
                    sharpe_ratio REAL,
                    calmar_ratio REAL,
                    
                    -- Basic risk metrics
                    max_drawdown REAL,
                    max_drawdown_duration_days INTEGER,
                    
                    -- Basic attribution metrics
                    long_return REAL,
                    short_return REAL,
                    
                    -- Basic trading metrics
                    turnover_rate REAL,
                    capacity_utilization REAL,
                    avg_slippage_bps REAL,
                    
                    -- Basic signal metrics
                    signal_strength_avg REAL,
                    
                    -- Metadata
                    calculation_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Basic portfolio-level metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS basic_portfolio_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    period_start DATETIME,
                    period_end DATETIME,
                    
                    -- Core performance metrics
                    total_return REAL,
                    annualized_return REAL,
                    volatility REAL,
                    sharpe_ratio REAL,
                    calmar_ratio REAL,
                    
                    -- Basic risk metrics
                    max_drawdown REAL,
                    max_drawdown_duration_days INTEGER,
                    
                    -- Basic attribution metrics
                    long_return REAL,
                    short_return REAL,
                    
                    -- Basic trading metrics
                    turnover_rate REAL,
                    capacity_utilization REAL,
                    avg_slippage_bps REAL,
                    
                    -- Portfolio composition (basic)
                    total_strategies INTEGER,
                    total_positions INTEGER,
                    long_positions INTEGER,
                    short_positions INTEGER,
                    total_capital REAL,
                    long_capital REAL,
                    short_capital REAL,
                    
                    -- Metadata
                    calculation_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Basic daily PnL tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS basic_daily_pnl (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    strategy_name TEXT,  -- NULL for aggregate portfolio
                    
                    -- Basic daily metrics
                    daily_return REAL,
                    cumulative_return REAL,
                    daily_pnl_usd REAL,
                    cumulative_pnl_usd REAL,
                    
                    -- Basic position metrics
                    positions_count INTEGER,
                    long_positions INTEGER,
                    short_positions INTEGER,
                    total_exposure_usd REAL,
                    
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, strategy_name)
                )
            """)
            
            # Basic position-level tracking table (simplified)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS basic_position_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    strategy_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    
                    -- Basic position details
                    side TEXT NOT NULL,  -- 'long' or 'short'
                    size_usd REAL,
                    weight REAL,
                    confidence REAL,
                    
                    -- Basic performance
                    daily_pnl_usd REAL,
                    
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, strategy_name, symbol)
                )
            """)
            
            # Create indexes for better query performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_basic_strategy_metrics_name_time ON basic_strategy_metrics(strategy_name, timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_basic_portfolio_metrics_time ON basic_portfolio_metrics(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_basic_daily_pnl_date_strategy ON basic_daily_pnl(date, strategy_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_basic_position_tracking_date_strategy ON basic_position_tracking(date, strategy_name)")
            
            conn.commit()
            self.logger.info("📊 Basic database schema initialized successfully")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper error handling"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def store_basic_strategy_metrics(self, strategy_name: str, metrics: BasicPerformanceMetrics) -> bool:
        """
        Store basic strategy performance metrics
        
        Args:
            strategy_name: Name of the strategy
            metrics: BasicPerformanceMetrics object
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO basic_strategy_metrics (
                        strategy_name, timestamp, period_start, period_end,
                        total_return, annualized_return, volatility, sharpe_ratio, calmar_ratio,
                        max_drawdown, max_drawdown_duration_days,
                        long_return, short_return,
                        turnover_rate, capacity_utilization, avg_slippage_bps,
                        signal_strength_avg
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    strategy_name,
                    metrics.calculation_timestamp,
                    metrics.period_start,
                    metrics.period_end,
                    metrics.total_return,
                    metrics.annualized_return,
                    metrics.volatility,
                    metrics.sharpe_ratio,
                    metrics.calmar_ratio,
                    metrics.max_drawdown,
                    metrics.max_drawdown_duration_days,
                    metrics.long_return,
                    metrics.short_return,
                    metrics.turnover_rate,
                    metrics.capacity_utilization,
                    metrics.avg_slippage_bps,
                    metrics.signal_strength_avg
                ))
                
                conn.commit()
                self.logger.debug(f"📊 Stored basic metrics for strategy: {strategy_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to store basic strategy metrics for {strategy_name}: {e}")
            return False
    
    def store_basic_portfolio_metrics(self, metrics: BasicPerformanceMetrics, 
                                    portfolio_stats: Dict[str, Any]) -> bool:
        """
        Store basic aggregate portfolio metrics
        
        Args:
            metrics: BasicPerformanceMetrics object for portfolio
            portfolio_stats: Additional portfolio statistics
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO basic_portfolio_metrics (
                        timestamp, period_start, period_end,
                        total_return, annualized_return, volatility, sharpe_ratio, calmar_ratio,
                        max_drawdown, max_drawdown_duration_days,
                        long_return, short_return,
                        turnover_rate, capacity_utilization, avg_slippage_bps,
                        total_strategies, total_positions, long_positions, short_positions,
                        total_capital, long_capital, short_capital
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    metrics.calculation_timestamp,
                    metrics.period_start,
                    metrics.period_end,
                    metrics.total_return,
                    metrics.annualized_return,
                    metrics.volatility,
                    metrics.sharpe_ratio,
                    metrics.calmar_ratio,
                    metrics.max_drawdown,
                    metrics.max_drawdown_duration_days,
                    metrics.long_return,
                    metrics.short_return,
                    metrics.turnover_rate,
                    metrics.capacity_utilization,
                    metrics.avg_slippage_bps,
                    portfolio_stats.get('total_strategies', 0),
                    portfolio_stats.get('total_positions', 0),
                    portfolio_stats.get('long_positions', 0),
                    portfolio_stats.get('short_positions', 0),
                    portfolio_stats.get('total_capital', 0),
                    portfolio_stats.get('long_capital', 0),
                    portfolio_stats.get('short_capital', 0)
                ))
                
                conn.commit()
                self.logger.debug("📊 Stored basic portfolio metrics")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to store basic portfolio metrics: {e}")
            return False
    
    def store_basic_daily_pnl(self, date: datetime, strategy_name: Optional[str], 
                            pnl_data: Dict[str, Any]) -> bool:
        """
        Store basic daily PnL data
        
        Args:
            date: Date for the PnL data
            strategy_name: Strategy name (None for aggregate)
            pnl_data: Dictionary containing basic PnL metrics
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO basic_daily_pnl (
                        date, strategy_name, daily_return, cumulative_return,
                        daily_pnl_usd, cumulative_pnl_usd, positions_count,
                        long_positions, short_positions, total_exposure_usd
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    date.date(),
                    strategy_name,
                    pnl_data.get('daily_return', 0),
                    pnl_data.get('cumulative_return', 0),
                    pnl_data.get('daily_pnl_usd', 0),
                    pnl_data.get('cumulative_pnl_usd', 0),
                    pnl_data.get('positions_count', 0),
                    pnl_data.get('long_positions', 0),
                    pnl_data.get('short_positions', 0),
                    pnl_data.get('total_exposure_usd', 0)
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to store basic daily PnL: {e}")
            return False
    
    def get_basic_strategy_metrics_history(self, strategy_name: str, 
                                         days: int = 30) -> List[Dict[str, Any]]:
        """
        Get basic historical metrics for a strategy
        
        Args:
            strategy_name: Name of the strategy
            days: Number of days to retrieve
            
        Returns:
            List of basic metric records
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM basic_strategy_metrics 
                    WHERE strategy_name = ? 
                    AND timestamp >= datetime('now', '-{} days')
                    ORDER BY timestamp DESC
                """.format(days), (strategy_name,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Failed to get basic strategy metrics history: {e}")
            return []
    
    def get_basic_portfolio_metrics_history(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        Get basic historical portfolio metrics
        
        Args:
            days: Number of days to retrieve
            
        Returns:
            List of basic metric records
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM basic_portfolio_metrics 
                    WHERE timestamp >= datetime('now', '-{} days')
                    ORDER BY timestamp DESC
                """.format(days))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Failed to get basic portfolio metrics history: {e}")
            return []
    
    def get_basic_daily_pnl_series(self, strategy_name: Optional[str] = None, 
                                 days: int = 30) -> List[Dict[str, Any]]:
        """
        Get basic daily PnL time series
        
        Args:
            strategy_name: Strategy name (None for aggregate)
            days: Number of days to retrieve
            
        Returns:
            List of basic daily PnL records
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                if strategy_name:
                    cursor.execute("""
                        SELECT * FROM basic_daily_pnl 
                        WHERE strategy_name = ? 
                        AND date >= date('now', '-{} days')
                        ORDER BY date ASC
                    """.format(days), (strategy_name,))
                else:
                    cursor.execute("""
                        SELECT * FROM basic_daily_pnl 
                        WHERE strategy_name IS NULL 
                        AND date >= date('now', '-{} days')
                        ORDER BY date ASC
                    """.format(days))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Failed to get basic daily PnL series: {e}")
            return []
