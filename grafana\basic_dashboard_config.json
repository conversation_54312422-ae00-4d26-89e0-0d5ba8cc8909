{"dashboard": {"id": null, "title": "Basic Multi-Strategy Portfolio Performance", "tags": ["trading", "portfolio", "basic-metrics"], "style": "dark", "timezone": "UTC", "refresh": "5m", "time": {"from": "now-30d", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "6h", "12h", "1d"]}, "panels": [{"id": 1, "title": "Portfolio PnL Curve", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"rawSql": "SELECT date as time, cumulative_pnl_usd as value FROM basic_daily_pnl WHERE strategy_name IS NULL ORDER BY date", "format": "time_series", "refId": "A"}], "yAxes": [{"label": "Cumulative PnL (USD)", "show": true}], "xAxis": {"show": true, "mode": "time"}, "legend": {"show": true, "values": false}}, {"id": 2, "title": "Strategy <PERSON>", "type": "stat", "gridPos": {"h": 6, "w": 8, "x": 0, "y": 8}, "targets": [{"rawSql": "SELECT strategy_name, sharpe_ratio FROM basic_strategy_metrics WHERE timestamp = (SELECT MAX(timestamp) FROM basic_strategy_metrics)", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 1.0}]}, "decimals": 2}}}, {"id": 3, "title": "Portfolio Annualized Return", "type": "stat", "gridPos": {"h": 6, "w": 8, "x": 8, "y": 8}, "targets": [{"rawSql": "SELECT annualized_return FROM basic_portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "decimals": 2, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 5}, {"color": "green", "value": 15}]}}}}, {"id": 4, "title": "Portfolio Volatility", "type": "stat", "gridPos": {"h": 6, "w": 8, "x": 16, "y": 8}, "targets": [{"rawSql": "SELECT volatility FROM basic_portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "decimals": 2, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 20}, {"color": "red", "value": 40}]}}}}, {"id": 5, "title": "Maximum Drawdown by Strategy", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "targets": [{"rawSql": "SELECT timestamp as time, max_drawdown as value, strategy_name as metric FROM basic_strategy_metrics ORDER BY timestamp", "format": "time_series", "refId": "A"}], "yAxes": [{"label": "Max Drawdown (%)", "show": true, "max": 0}]}, {"id": 6, "title": "Long vs Short Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "targets": [{"rawSql": "SELECT timestamp as time, long_return as 'Long Return', short_return as 'Short Return' FROM basic_portfolio_metrics ORDER BY timestamp", "format": "time_series", "refId": "A"}], "yAxes": [{"label": "Return (%)", "show": true}]}, {"id": 7, "title": "Port<PERSON>lio <PERSON>", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 0, "y": 22}, "targets": [{"rawSql": "SELECT calmar_ratio FROM basic_portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"decimals": 2, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 1.0}]}}}}, {"id": 8, "title": "Portfolio Turnover", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 22}, "targets": [{"rawSql": "SELECT turnover_rate FROM basic_portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "decimals": 2}}}, {"id": 9, "title": "Capacity Utilization", "type": "gauge", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 22}, "targets": [{"rawSql": "SELECT capacity_utilization FROM basic_portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 0.8}]}}}}, {"id": 10, "title": "Average Slippage", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 22}, "targets": [{"rawSql": "SELECT avg_slippage_bps FROM basic_portfolio_metrics ORDER BY timestamp DESC LIMIT 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "decimals": 1, "displayName": "Slippage (bps)"}}}, {"id": 11, "title": "Daily Returns Distribution", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}, "targets": [{"rawSql": "SELECT date as time, daily_return as value FROM basic_daily_pnl WHERE strategy_name IS NULL ORDER BY date", "format": "time_series", "refId": "A"}], "yAxes": [{"label": "Daily Return (%)", "show": true}]}, {"id": 12, "title": "Strategy Performance Comparison", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 26}, "targets": [{"rawSql": "SELECT strategy_name as 'Strategy', annualized_return as 'Annual Return (%)', sharpe_ratio as 'Sharpe Ratio', max_drawdown as 'Max Drawdown (%)' FROM basic_strategy_metrics WHERE timestamp = (SELECT MAX(timestamp) FROM basic_strategy_metrics)", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"align": "center"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Annual Return (%)"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON>"}, "properties": [{"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Max Drawdown (%)"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "decimals", "value": 2}]}]}}]}}