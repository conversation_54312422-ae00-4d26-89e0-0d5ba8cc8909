# Basic Portfolio Metrics System Setup

## Overview

This document describes the **Basic Portfolio Metrics System** - a simplified version that tracks only the essential 11 metrics you requested, with advanced features temporarily disabled for easier implementation and testing.

## ✅ Basic Metrics Enabled

The system tracks these **11 essential metrics** for each strategy and the aggregate portfolio:

1. **PnL Curve** - Cumulative arithmetic returns over time
2. **Sharpe Ratio** - Risk-adjusted returns using arithmetic returns
3. **Calmar Ratio** - Return/max drawdown ratio
4. **Annualized Returns** - Arithmetic annual return
5. **Max Drawdown** - Peak-to-trough decline
6. **Max Time to Recovery** - Days to recover from drawdown
7. **Portfolio Ranking & Exposures** - Signal strength distribution
8. **Performance Attribution** - Long vs short leg contribution
9. **Turnover** - Portfolio turnover rate
10. **Capacity Utilization** - Capital deployment efficiency
11. **Slippage** - Execution cost analysis

## ❌ Advanced Features Temporarily Disabled

These features are available in the codebase but disabled in configuration:

- VaR/CVaR calculations
- Correlation analysis
- Market beta calculations
- Factor exposure analysis
- Sector concentration analysis
- Intraday performance tracking
- Strategy-specific metrics (funding capture, momentum persistence, etc.)
- Advanced execution analytics

## 🏗️ Architecture Components

### Core Files Created

```
src/portfolio/
├── basic_performance_tracker.py      # Main orchestration (simplified)
├── basic_metrics_calculator.py       # Essential metrics calculation
├── basic_metrics_database.py         # SQLite storage (simplified schema)
└── __init__.py                       # Updated imports

config/
└── metrics_config.yaml               # Configuration (advanced features disabled)

grafana/
├── basic_dashboard_config.json       # Simplified dashboard (12 panels)
└── basic_datasource_config.json      # Data source configuration

scripts/
└── setup_basic_metrics.py            # Setup script

docs/
└── BASIC_METRICS_SETUP.md           # This document
```

### Configuration Changes

Updated `config.yaml` with basic metrics settings:

```yaml
basic_metrics:
  enable_basic_metrics: true
  metrics_calculation_frequency_hours: 6  # Reduced frequency
  enable_real_time_tracking: true
  metrics_database_path: "data/basic_portfolio_metrics.db"
  
  # Individual metric toggles (all enabled)
  track_pnl_curve: true
  track_sharpe_ratio: true
  track_calmar_ratio: true
  # ... (all 11 metrics enabled)
```

## 🚀 Quick Setup

### 1. Run Setup Script

```bash
python scripts/setup_basic_metrics.py
```

This will:
- Install minimal dependencies (numpy only)
- Create necessary directories
- Initialize SQLite database with basic schema
- Create Grafana configuration files
- Validate setup

### 2. Install Dependencies

```bash
pip install numpy>=1.21.0
```

### 3. Configure Grafana

1. **Add Data Source:**
   - Import configuration from `grafana/basic_datasource_config.json`
   - Data source name: `Basic_Portfolio_Metrics_DB`
   - Type: SQLite
   - Path: `data/basic_portfolio_metrics.db`

2. **Import Dashboard:**
   - Import dashboard from `grafana/basic_dashboard_config.json`
   - 12 panels covering all basic metrics

### 4. Enable in Your System

The basic metrics system will automatically integrate with your existing `MultiStrategyOrchestrator` when `enable_basic_metrics: true` is set in config.

## 📊 Database Schema (Simplified)

### Tables Created

1. **`basic_strategy_metrics`** - Core metrics per strategy
2. **`basic_portfolio_metrics`** - Aggregate portfolio metrics  
3. **`basic_daily_pnl`** - Daily PnL time series
4. **`basic_position_tracking`** - Position-level data (simplified)

### Sample Queries

**Portfolio PnL Curve:**
```sql
SELECT date as time, cumulative_pnl_usd as value 
FROM basic_daily_pnl 
WHERE strategy_name IS NULL 
ORDER BY date
```

**Strategy Performance Comparison:**
```sql
SELECT strategy_name, sharpe_ratio, annualized_return, max_drawdown 
FROM basic_strategy_metrics 
WHERE timestamp = (SELECT MAX(timestamp) FROM basic_strategy_metrics)
```

## 📈 Grafana Dashboard Panels

The basic dashboard includes 12 panels:

1. **Portfolio PnL Curve** - Time series of cumulative returns
2. **Strategy Sharpe Ratios** - Current Sharpe ratios by strategy
3. **Portfolio Annualized Return** - Current annual return
4. **Portfolio Volatility** - Current volatility
5. **Maximum Drawdown by Strategy** - Drawdown over time
6. **Long vs Short Performance** - Attribution analysis
7. **Portfolio Calmar Ratio** - Risk-adjusted performance
8. **Portfolio Turnover** - Trading frequency
9. **Capacity Utilization** - Capital deployment gauge
10. **Average Slippage** - Execution cost
11. **Daily Returns Distribution** - Return volatility
12. **Strategy Performance Table** - Comparison table

## ⚙️ Configuration Options

### Calculation Frequency
```yaml
metrics_calculation_frequency_hours: 6  # Calculate every 6 hours
```

### Data Retention
```yaml
max_performance_history_days: 30  # Keep 30 days of data
```

### Individual Metric Toggles
```yaml
track_pnl_curve: true              # Enable/disable individual metrics
track_sharpe_ratio: true
# ... etc
```

## 🔄 Integration with Existing System

The basic metrics system integrates with your current architecture:

1. **Data Collection:** Hooks into existing strategy execution flow
2. **Calculation:** Runs every 6 hours (configurable)
3. **Storage:** SQLite database with automatic cleanup
4. **Visualization:** Grafana dashboard with real-time updates

## 📝 Usage Example

```python
from portfolio import BasicPerformanceTracker

# Initialize (automatically done by orchestrator)
config = load_config()
tracker = BasicPerformanceTracker(config)

# Record strategy execution (automatic)
await tracker.record_strategy_execution(
    strategy_name="stat_arb_carry_trade",
    strategy_result=result,
    final_positions=positions
)

# Get real-time metrics
metrics = await tracker.calculate_real_time_basic_metrics("stat_arb_carry_trade")
print(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
```

## 🔧 Troubleshooting

### Common Issues

1. **Database not found:**
   ```bash
   python scripts/setup_basic_metrics.py
   ```

2. **Grafana connection issues:**
   - Check database path in data source configuration
   - Ensure SQLite plugin is installed in Grafana

3. **No data in dashboard:**
   - Verify `enable_basic_metrics: true` in config.yaml
   - Check logs for calculation errors
   - Ensure strategies are running and recording data

### Validation

Run validation script:
```bash
python scripts/setup_basic_metrics.py
```

Check the validation section for any failed components.

## 🚀 Next Steps

1. **Test Basic System:** Run with basic metrics for a few days
2. **Monitor Performance:** Check Grafana dashboard regularly
3. **Validate Accuracy:** Compare calculated metrics with expected values
4. **Enable Advanced Features:** Later, switch to enhanced metrics system

## 🔄 Upgrading to Enhanced Metrics

When ready for advanced features:

1. Change configuration:
   ```yaml
   basic_metrics:
     enable_basic_metrics: false
   enhanced_metrics:
     enable_enhanced_metrics: true
   ```

2. Run enhanced setup:
   ```bash
   python scripts/setup_metrics.py
   ```

3. Import enhanced Grafana dashboard

The basic system provides a solid foundation and can be easily upgraded to the full enhanced system when needed.

## 📞 Support

- Check logs in `logs/` directory for errors
- Validate setup using the setup script
- Review configuration in `config.yaml` and `config/metrics_config.yaml`
- Test database connectivity manually if needed

This basic implementation focuses on the essential metrics you requested while keeping the system simple and reliable.
