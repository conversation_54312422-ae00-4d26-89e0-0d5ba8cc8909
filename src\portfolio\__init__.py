"""
Portfolio management and performance tracking module

This module provides functionality for combining multiple strategy portfolios
and tracking comprehensive performance metrics for multi-strategy trading systems.

The system uses a basic metrics approach focused on essential performance indicators
for investor reporting and real-time monitoring via Grafana dashboards.
"""

from .combiner import PortfolioCombiner, CombinedPosition
from .performance_tracker import PerformanceTracker, StrategyPerformance

# Basic metrics system (PRIMARY - ENABLED)
from .basic_performance_tracker import BasicPerformanceTracker
from .basic_metrics_calculator import BasicMetricsCalculator, BasicPerformanceMetrics
from .basic_metrics_database import BasicMetricsDatabase

# Enhanced metrics system (AVAILABLE FOR FUTURE USE)
from .enhanced_performance_tracker import EnhancedPerformanceTracker
from .metrics_calculator import MetricsCalculator, PerformanceMetrics
from .metrics_database import MetricsDatabase

__all__ = [
    # Core portfolio management
    'PortfolioCombiner',
    'CombinedPosition',
    'PerformanceTracker',
    'StrategyPerformance',

    # Basic metrics system (PRIMARY)
    'BasicPerformanceTracker',
    'BasicMetricsCalculator',
    'BasicPerformanceMetrics',
    'BasicMetricsDatabase',

    # Enhanced metrics system (FUTURE USE)
    'EnhancedPerformanceTracker',
    'MetricsCalculator',
    'PerformanceMetrics',
    'MetricsDatabase'
]
