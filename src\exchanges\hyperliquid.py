"""
Hyperliquid exchange implementation for StatArb Carry Trade Strategy
"""

import ccxt.async_support as ccxt
from typing import Dict, List, Optional, Any
from utils import get_logger
from exchanges.base import ExchangeInterface

logger = get_logger(__name__)


class HyperliquidExchange(ExchangeInterface):
    """Hyperliquid exchange implementation"""

    def __init__(self):
        self.exchange = None
        self._name = "Hyperliquid"

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize Hyperliquid exchange connection with error handling"""
        try:
            # Get CCXT configuration
            ccxt_config = config.get('ccxt_config', {})
            hyperliquid_options = ccxt_config.get('hyperliquid_options', {})

            # Hyperliquid uses different authentication method
            wallet_address = config.get('wallet_address', '').strip()
            private_key = config.get('private_key', '').strip()

            # Prepare constructor parameters with CCXT configuration
            constructor_params = {
                'enableRateLimit': ccxt_config.get('enable_rate_limit', True),
                'timeout': hyperliquid_options.get('timeout', ccxt_config.get('timeout', 30000)),
                'rateLimit': hyperliquid_options.get('rateLimit', ccxt_config.get('rateLimit', 500)),
            }

            # Add authentication if provided
            if wallet_address and private_key:
                constructor_params['walletAddress'] = wallet_address
                constructor_params['privateKey'] = private_key
                logger.info("🔑 Using wallet address and private key for private endpoints")
            else:
                logger.info("🌐 Using public endpoints only (no credentials provided)")

            self.exchange = ccxt.hyperliquid(constructor_params)

            # Handle different trading modes
            if config.get('use_testnet', False):
                # Hyperliquid testnet support
                if hasattr(self.exchange, 'set_sandbox_mode'):
                    self.exchange.set_sandbox_mode(True)
                    logger.info("🧪 Hyperliquid testnet mode enabled")
                else:
                    # Manual testnet URL override if needed
                    logger.info("🔧 Setting testnet URLs manually")
                    self.exchange.urls['api'] = 'https://api.hyperliquid-testnet.xyz'
                    logger.info("🧪 Hyperliquid testnet mode enabled via manual URL override")
            
            # Note: Hyperliquid doesn't have demo mode mentioned in docs

            # Test public endpoints first
            try:
                markets = await self.fetch_markets()

                # Determine mode for logging
                if config.get('use_testnet', False):
                    mode = "testnet"
                else:
                    mode = "live"

                logger.info(f"✅ Connected to {self._name} {mode}")
                logger.info(f"✅ Found {len(markets)} markets")

                # Test a public ticker to ensure connectivity
                if markets:
                    # Try to find a common symbol that's likely to have data
                    test_symbols = ['BTC/USD:USD', 'ETH/USD:USD', 'BTC/USDC', 'ETH/USDC']
                    test_symbol = None

                    for symbol in test_symbols:
                        if symbol in markets:
                            test_symbol = symbol
                            break

                    if not test_symbol:
                        # Fallback to first available symbol
                        test_symbol = list(markets.keys())[0]

                    try:
                        ticker = await self.fetch_ticker(test_symbol)
                        logger.info(f"✅ Public API test successful with {test_symbol}")
                    except Exception as e:
                        logger.warning(f"⚠️ Ticker test failed for {test_symbol}: {e}")
                        logger.info("✅ Markets loaded successfully, continuing without ticker test")

                # Test private endpoints if credentials provided
                if wallet_address and private_key:
                    try:
                        await self.fetch_balance()
                        logger.info("✅ Private API test successful")
                    except Exception as e:
                        logger.warning(f"⚠️ Private API test failed: {e}")
                        logger.info("🌐 Continuing with public endpoints only")

                return True

            except Exception as e:
                logger.error(f"❌ Failed to test {self._name} connection: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to initialize {self._name} exchange: {e}")
            return False

    async def fetch_markets(self) -> Dict[str, Any]:
        """Fetch available markets"""
        try:
            markets = await self.exchange.load_markets()
            return markets
        except Exception as e:
            logger.error(f"❌ Failed to fetch markets from {self._name}: {e}")
            raise

    async def fetch_tickers(self) -> Dict[str, Any]:
        """Fetch ticker data for all symbols"""
        try:
            tickers = await self.exchange.fetch_tickers()
            return tickers
        except Exception as e:
            logger.error(f"❌ Failed to fetch tickers from {self._name}: {e}")
            raise

    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """Fetch ticker data for a single symbol"""
        try:
            ticker = await self.exchange.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            logger.error(f"❌ Failed to fetch ticker for {symbol} from {self._name}: {e}")
            raise

    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List[List]:
        """Fetch OHLCV data"""
        try:
            ohlcv = await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            return ohlcv
        except Exception as e:
            logger.error(f"❌ Failed to fetch OHLCV for {symbol} from {self._name}: {e}")
            raise

    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """Fetch current funding rate"""
        try:
            funding_rate = await self.exchange.fetch_funding_rate(symbol)
            return funding_rate
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate for {symbol} from {self._name}: {e}")
            raise

    async def fetch_funding_rate_history(self, symbol: str, limit: int) -> List[Dict]:
        """Fetch funding rate history"""
        try:
            history = await self.exchange.fetch_funding_rate_history(symbol, limit=limit)
            return history
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate history for {symbol} from {self._name}: {e}")
            raise

    async def fetch_balance(self) -> Dict[str, Any]:
        """Fetch account balance"""
        try:
            balance = await self.exchange.fetch_balance()
            return balance
        except Exception as e:
            logger.error(f"❌ Failed to fetch balance from {self._name}: {e}")
            raise

    async def fetch_positions(self, symbols: Optional[List[str]] = None) -> List[Dict]:
        """Fetch positions"""
        try:
            positions = await self.exchange.fetch_positions(symbols)
            return positions
        except Exception as e:
            logger.error(f"❌ Failed to fetch positions from {self._name}: {e}")
            raise

    async def fetch_open_orders(self, symbol: str) -> List[Dict]:
        """Fetch open orders"""
        try:
            orders = await self.exchange.fetch_open_orders(symbol)
            return orders
        except Exception as e:
            logger.error(f"❌ Failed to fetch open orders for {symbol} from {self._name}: {e}")
            raise

    async def fetch_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Fetch order status"""
        try:
            order = await self.exchange.fetch_order(order_id, symbol)
            return order
        except Exception as e:
            logger.error(f"❌ Failed to fetch order {order_id} for {symbol} from {self._name}: {e}")
            raise

    async def fetch_order_book(self, symbol: str) -> Dict[str, Any]:
        """Fetch order book"""
        try:
            order_book = await self.exchange.fetch_order_book(symbol)
            return order_book
        except Exception as e:
            logger.error(f"❌ Failed to fetch order book for {symbol} from {self._name}: {e}")
            raise

    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict[str, Any]:
        """Create limit order"""
        try:
            # Note: Hyperliquid doesn't support market orders, so all orders are limit orders
            order = await self.exchange.create_limit_order(symbol, side, amount, price, params)
            return order
        except Exception as e:
            logger.error(f"❌ Failed to create limit order for {symbol} on {self._name}: {e}")
            raise

    async def create_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict[str, Any]:
        """Create market order (simulated as limit order on Hyperliquid)"""
        try:
            # Hyperliquid doesn't support market orders, CCXT will simulate
            order = await self.exchange.create_market_order(symbol, side, amount, params)
            return order
        except Exception as e:
            logger.error(f"❌ Failed to create market order for {symbol} on {self._name}: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Cancel order"""
        try:
            result = await self.exchange.cancel_order(order_id, symbol)
            return result
        except Exception as e:
            logger.error(f"❌ Failed to cancel order {order_id} for {symbol} on {self._name}: {e}")
            raise

    @property
    def name(self) -> str:
        """Exchange name"""
        return self._name
