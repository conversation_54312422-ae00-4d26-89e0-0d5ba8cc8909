#!/usr/bin/env python3
"""
Setup script for enhanced portfolio metrics system

This script installs required dependencies and initializes the metrics database.
"""

import sys
import subprocess
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from portfolio.metrics_database import MetricsDatabase
from config import load_config

logger = logging.getLogger(__name__)


def install_dependencies():
    """Install required Python packages"""
    required_packages = [
        "numpy>=1.21.0",
        "pandas>=1.3.0",
        "sqlite3",  # Usually included with Python
    ]
    
    print("📦 Installing required dependencies...")
    
    for package in required_packages:
        if package == "sqlite3":
            # sqlite3 is usually included with Python
            try:
                import sqlite3
                print(f"✅ {package} - already available")
            except ImportError:
                print(f"❌ {package} - not available (install Python with sqlite3 support)")
                return False
        else:
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package
                ])
                print(f"✅ {package} - installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install {package}: {e}")
                return False
    
    return True


def create_directories():
    """Create necessary directories"""
    directories = [
        "data",
        "grafana",
        "grafana/data",
        "reports",
        "backups",
        "logs"
    ]
    
    print("📁 Creating directories...")
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def initialize_database():
    """Initialize the metrics database"""
    print("🗄️ Initializing metrics database...")
    
    try:
        # Load configuration
        config = load_config()
        
        # Get database path from config
        db_path = config.get('enhanced_metrics', {}).get('metrics_database_path', 'data/portfolio_metrics.db')
        
        # Initialize database
        metrics_db = MetricsDatabase(db_path)
        
        print(f"✅ Database initialized: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        return False


def setup_grafana_config():
    """Setup Grafana configuration files"""
    print("📊 Setting up Grafana configuration...")
    
    try:
        # Copy dashboard configuration
        dashboard_config_path = Path("grafana/dashboard_config.json")
        
        if dashboard_config_path.exists():
            print("✅ Grafana dashboard configuration already exists")
        else:
            print("⚠️ Grafana dashboard configuration not found")
            print("   Please ensure grafana/dashboard_config.json exists")
        
        # Create data source configuration
        datasource_config = {
            "name": "Portfolio_Metrics_DB",
            "type": "sqlite",
            "url": "data/portfolio_metrics.db",
            "access": "direct",
            "isDefault": True
        }
        
        datasource_path = Path("grafana/datasource_config.json")
        
        import json
        with open(datasource_path, 'w') as f:
            json.dump(datasource_config, f, indent=2)
        
        print(f"✅ Created Grafana data source configuration: {datasource_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup Grafana configuration: {e}")
        return False


def validate_setup():
    """Validate the setup"""
    print("🔍 Validating setup...")
    
    validation_checks = []
    
    # Check if database exists and is accessible
    try:
        config = load_config()
        db_path = config.get('enhanced_metrics', {}).get('metrics_database_path', 'data/portfolio_metrics.db')
        
        if Path(db_path).exists():
            # Try to connect to database
            metrics_db = MetricsDatabase(db_path)
            validation_checks.append(("Database connectivity", True))
        else:
            validation_checks.append(("Database exists", False))
    except Exception as e:
        validation_checks.append(("Database connectivity", False, str(e)))
    
    # Check if required directories exist
    required_dirs = ["data", "grafana", "reports", "logs"]
    for directory in required_dirs:
        exists = Path(directory).exists()
        validation_checks.append((f"Directory {directory}", exists))
    
    # Check if configuration files exist
    config_files = [
        "config.yaml",
        "config/metrics_config.yaml",
        "grafana/dashboard_config.json"
    ]
    for config_file in config_files:
        exists = Path(config_file).exists()
        validation_checks.append((f"Config file {config_file}", exists))
    
    # Print validation results
    all_passed = True
    for check in validation_checks:
        if len(check) == 2:
            name, passed = check
            status = "✅" if passed else "❌"
            print(f"{status} {name}")
            if not passed:
                all_passed = False
        else:
            name, passed, error = check
            status = "✅" if passed else "❌"
            print(f"{status} {name}")
            if not passed:
                print(f"   Error: {error}")
                all_passed = False
    
    return all_passed


def main():
    """Main setup function"""
    print("🚀 Setting up Enhanced Portfolio Metrics System")
    print("=" * 50)
    
    steps = [
        ("Installing dependencies", install_dependencies),
        ("Creating directories", create_directories),
        ("Initializing database", initialize_database),
        ("Setting up Grafana config", setup_grafana_config),
        ("Validating setup", validate_setup)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        success = step_func()
        
        if not success:
            print(f"❌ Setup failed at step: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 Enhanced Portfolio Metrics System setup completed successfully!")
    print("\nNext steps:")
    print("1. Review configuration in config/metrics_config.yaml")
    print("2. Import Grafana dashboard from grafana/dashboard_config.json")
    print("3. Configure Grafana data source using grafana/datasource_config.json")
    print("4. Run your trading system with enhanced metrics enabled")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
