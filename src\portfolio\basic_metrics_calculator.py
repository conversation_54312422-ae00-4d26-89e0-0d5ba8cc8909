"""
Basic portfolio metrics calculator for multi-strategy trading system

This module calculates essential performance metrics using arithmetic returns
for individual strategies and aggregate portfolios. Focuses on core metrics only.
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class BasicPerformanceMetrics:
    """Essential performance metrics for a strategy or portfolio"""
    
    # Core performance metrics (ENABLED)
    total_return: float = 0.0
    annualized_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # Risk metrics (BASIC ONLY)
    max_drawdown: float = 0.0
    max_drawdown_duration_days: int = 0
    
    # Performance attribution (BASIC ONLY)
    long_return: float = 0.0
    short_return: float = 0.0
    
    # Trading metrics (BASIC ONLY)
    turnover_rate: float = 0.0
    capacity_utilization: float = 0.0
    avg_slippage_bps: float = 0.0
    
    # Signal strength (BASIC ONLY)
    signal_strength_avg: float = 0.0
    
    # Timing metrics
    calculation_timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    period_start: Optional[datetime] = None
    period_end: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'calmar_ratio': self.calmar_ratio,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_duration_days': self.max_drawdown_duration_days,
            'long_return': self.long_return,
            'short_return': self.short_return,
            'turnover_rate': self.turnover_rate,
            'capacity_utilization': self.capacity_utilization,
            'avg_slippage_bps': self.avg_slippage_bps,
            'signal_strength_avg': self.signal_strength_avg,
            'calculation_timestamp': self.calculation_timestamp.isoformat(),
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None
        }


class BasicMetricsCalculator:
    """
    Basic metrics calculator for portfolio performance analysis
    
    Calculates essential performance metrics using arithmetic returns
    for both individual strategies and aggregate portfolios.
    """
    
    def __init__(self, risk_free_rate: float = 0.02):
        """
        Initialize basic metrics calculator
        
        Args:
            risk_free_rate: Annual risk-free rate for Sharpe ratio calculation
        """
        self.risk_free_rate = risk_free_rate
        self.logger = logging.getLogger(__name__)
    
    def calculate_basic_metrics(self, 
                               returns_series: List[float],
                               positions_history: List[Dict[str, Any]],
                               execution_history: List[Dict[str, Any]],
                               timestamps: List[datetime]) -> BasicPerformanceMetrics:
        """
        Calculate basic metrics for a strategy or portfolio
        
        Args:
            returns_series: List of arithmetic returns
            positions_history: Historical position data
            execution_history: Historical execution data
            timestamps: Corresponding timestamps
            
        Returns:
            BasicPerformanceMetrics object with calculated metrics
        """
        if not returns_series or len(returns_series) < 2:
            self.logger.warning("Insufficient data for basic metrics calculation")
            return BasicPerformanceMetrics()
        
        try:
            # Convert to numpy arrays for calculations
            returns = np.array(returns_series)
            
            # Core performance metrics
            total_return = self._calculate_total_return(returns)
            annualized_return = self._calculate_annualized_return(returns, len(timestamps))
            volatility = self._calculate_volatility(returns)
            sharpe_ratio = self._calculate_sharpe_ratio(returns, volatility)
            
            # Risk metrics (basic only)
            max_drawdown, max_dd_duration = self._calculate_max_drawdown(returns)
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Performance attribution (basic only)
            long_return, short_return = self._calculate_basic_attribution(
                positions_history, returns_series
            )
            
            # Trading metrics (basic only)
            turnover_rate = self._calculate_basic_turnover(positions_history)
            capacity_utilization = self._calculate_basic_capacity_utilization(positions_history)
            avg_slippage = self._calculate_basic_slippage(execution_history)
            
            # Signal strength (basic only)
            signal_avg = self._calculate_basic_signal_strength(positions_history)
            
            return BasicPerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                calmar_ratio=calmar_ratio,
                max_drawdown=max_drawdown,
                max_drawdown_duration_days=max_dd_duration,
                long_return=long_return,
                short_return=short_return,
                turnover_rate=turnover_rate,
                capacity_utilization=capacity_utilization,
                avg_slippage_bps=avg_slippage,
                signal_strength_avg=signal_avg,
                period_start=timestamps[0] if timestamps else None,
                period_end=timestamps[-1] if timestamps else None
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating basic metrics: {e}")
            return BasicPerformanceMetrics()
    
    def _calculate_total_return(self, returns: np.ndarray) -> float:
        """Calculate total arithmetic return"""
        return float(np.sum(returns))
    
    def _calculate_annualized_return(self, returns: np.ndarray, num_periods: int) -> float:
        """Calculate annualized arithmetic return"""
        if num_periods == 0:
            return 0.0
        
        # Assume daily returns, convert to annual
        total_return = np.sum(returns)
        days_per_year = 365.25
        years = num_periods / days_per_year
        
        if years <= 0:
            return 0.0
        
        return float(total_return / years)
    
    def _calculate_volatility(self, returns: np.ndarray) -> float:
        """Calculate annualized volatility"""
        if len(returns) < 2:
            return 0.0
        
        daily_vol = np.std(returns, ddof=1)
        return float(daily_vol * np.sqrt(365.25))
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray, volatility: float) -> float:
        """Calculate Sharpe ratio using arithmetic returns"""
        if volatility == 0:
            return 0.0
        
        avg_return = np.mean(returns)
        daily_rf_rate = self.risk_free_rate / 365.25
        excess_return = avg_return - daily_rf_rate
        
        return float(excess_return * np.sqrt(365.25) / volatility)
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> Tuple[float, int]:
        """Calculate maximum drawdown and duration (basic version)"""
        if len(returns) == 0:
            return 0.0, 0
        
        # Calculate cumulative returns
        cumulative = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = cumulative - running_max
        
        max_drawdown = float(np.min(drawdown))
        
        # Calculate max drawdown duration (simplified)
        max_duration = 0
        current_duration = 0
        
        for dd in drawdown:
            if dd < 0:
                current_duration += 1
                max_duration = max(max_duration, current_duration)
            else:
                current_duration = 0
        
        return max_drawdown, max_duration
    
    def _calculate_basic_attribution(self, positions_history: List[Dict[str, Any]], 
                                   returns_series: List[float]) -> Tuple[float, float]:
        """Calculate basic performance attribution between long and short positions"""
        if not positions_history or not returns_series:
            return 0.0, 0.0
        
        try:
            long_returns = []
            short_returns = []
            
            for i, positions in enumerate(positions_history):
                if i >= len(returns_series):
                    break
                
                daily_return = returns_series[i]
                long_exposure = sum(pos.get('size_usd', 0) for pos in positions if pos.get('side') == 'long')
                short_exposure = sum(pos.get('size_usd', 0) for pos in positions if pos.get('side') == 'short')
                total_exposure = long_exposure + short_exposure
                
                if total_exposure > 0:
                    long_weight = long_exposure / total_exposure
                    short_weight = short_exposure / total_exposure
                    
                    long_returns.append(daily_return * long_weight)
                    short_returns.append(daily_return * short_weight)
            
            # Calculate total returns for each leg
            long_total = sum(long_returns) if long_returns else 0.0
            short_total = sum(short_returns) if short_returns else 0.0
            
            return float(long_total), float(short_total)
            
        except Exception as e:
            self.logger.error(f"Error calculating basic attribution: {e}")
            return 0.0, 0.0
    
    def _calculate_basic_turnover(self, positions_history: List[Dict[str, Any]]) -> float:
        """Calculate basic portfolio turnover rate"""
        if len(positions_history) < 2:
            return 0.0
        
        try:
            total_turnover = 0.0
            periods = 0
            
            for i in range(1, len(positions_history)):
                prev_positions = {pos.get('symbol'): pos.get('size_usd', 0) 
                                for pos in positions_history[i-1]}
                curr_positions = {pos.get('symbol'): pos.get('size_usd', 0) 
                                for pos in positions_history[i]}
                
                # Calculate turnover for this period
                all_symbols = set(prev_positions.keys()) | set(curr_positions.keys())
                period_turnover = 0.0
                total_value = 0.0
                
                for symbol in all_symbols:
                    prev_size = prev_positions.get(symbol, 0)
                    curr_size = curr_positions.get(symbol, 0)
                    period_turnover += abs(curr_size - prev_size)
                    total_value += max(prev_size, curr_size)
                
                if total_value > 0:
                    total_turnover += period_turnover / total_value
                    periods += 1
            
            return float(total_turnover / periods) if periods > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating basic turnover: {e}")
            return 0.0
    
    def _calculate_basic_capacity_utilization(self, positions_history: List[Dict[str, Any]]) -> float:
        """Calculate basic capacity utilization"""
        if not positions_history:
            return 0.0
        
        try:
            utilizations = []
            
            for positions in positions_history:
                total_deployed = sum(pos.get('size_usd', 0) for pos in positions)
                utilizations.append(total_deployed)
            
            if not utilizations:
                return 0.0
            
            max_capacity = max(utilizations)
            avg_deployed = np.mean(utilizations)
            
            return float(avg_deployed / max_capacity) if max_capacity > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating basic capacity utilization: {e}")
            return 0.0
    
    def _calculate_basic_slippage(self, execution_history: List[Dict[str, Any]]) -> float:
        """Calculate basic average slippage in basis points"""
        if not execution_history:
            return 0.0
        
        try:
            slippages = []
            
            for execution in execution_history:
                expected_price = execution.get('expected_price', 0)
                actual_price = execution.get('actual_price', 0)
                
                if expected_price > 0:
                    slippage_pct = abs(actual_price - expected_price) / expected_price
                    slippages.append(slippage_pct * 10000)  # Convert to basis points
            
            return float(np.mean(slippages)) if slippages else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating basic slippage: {e}")
            return 0.0
    
    def _calculate_basic_signal_strength(self, positions_history: List[Dict[str, Any]]) -> float:
        """Calculate basic signal strength average"""
        if not positions_history:
            return 0.0
        
        try:
            all_confidences = []
            
            for positions in positions_history:
                for pos in positions:
                    confidence = pos.get('confidence', 0)
                    if confidence > 0:
                        all_confidences.append(confidence)
            
            return float(np.mean(all_confidences)) if all_confidences else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating basic signal strength: {e}")
            return 0.0
