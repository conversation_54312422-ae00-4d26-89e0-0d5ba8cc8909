#!/usr/bin/env python3
"""
Setup script for basic portfolio metrics system

This script installs required dependencies and initializes the basic metrics database.
"""

import sys
import subprocess
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from portfolio.basic_metrics_database import BasicMetricsDatabase
from config import load_config

logger = logging.getLogger(__name__)


def install_basic_dependencies():
    """Install required Python packages for basic metrics"""
    required_packages = [
        "numpy>=1.21.0",
    ]
    
    print("📦 Installing basic dependencies...")
    
    for package in required_packages:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ])
            print(f"✅ {package} - installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    # Check sqlite3 (usually included with Python)
    try:
        import sqlite3
        print(f"✅ sqlite3 - already available")
    except ImportError:
        print(f"❌ sqlite3 - not available (install Python with sqlite3 support)")
        return False
    
    return True


def create_basic_directories():
    """Create necessary directories for basic metrics"""
    directories = [
        "data",
        "grafana",
        "grafana/data",
        "logs"
    ]
    
    print("📁 Creating basic directories...")
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def initialize_basic_database():
    """Initialize the basic metrics database"""
    print("🗄️ Initializing basic metrics database...")
    
    try:
        # Load configuration
        config = load_config()
        
        # Get database path from config
        basic_metrics_config = config.get('basic_metrics', {})
        db_path = basic_metrics_config.get('metrics_database_path', 'data/basic_portfolio_metrics.db')
        
        # Initialize database
        basic_metrics_db = BasicMetricsDatabase(db_path)
        
        print(f"✅ Basic database initialized: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize basic database: {e}")
        return False


def setup_basic_grafana_config():
    """Setup basic Grafana configuration files"""
    print("📊 Setting up basic Grafana configuration...")
    
    try:
        # Check basic dashboard configuration
        basic_dashboard_config_path = Path("grafana/basic_dashboard_config.json")
        
        if basic_dashboard_config_path.exists():
            print("✅ Basic Grafana dashboard configuration exists")
        else:
            print("⚠️ Basic Grafana dashboard configuration not found")
            print("   Please ensure grafana/basic_dashboard_config.json exists")
        
        # Create basic data source configuration
        basic_datasource_config = {
            "name": "Basic_Portfolio_Metrics_DB",
            "type": "sqlite",
            "url": "data/basic_portfolio_metrics.db",
            "access": "direct",
            "isDefault": True
        }
        
        basic_datasource_path = Path("grafana/basic_datasource_config.json")
        
        import json
        with open(basic_datasource_path, 'w') as f:
            json.dump(basic_datasource_config, f, indent=2)
        
        print(f"✅ Created basic Grafana data source configuration: {basic_datasource_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup basic Grafana configuration: {e}")
        return False


def validate_basic_setup():
    """Validate the basic setup"""
    print("🔍 Validating basic setup...")
    
    validation_checks = []
    
    # Check if basic database exists and is accessible
    try:
        config = load_config()
        basic_metrics_config = config.get('basic_metrics', {})
        db_path = basic_metrics_config.get('metrics_database_path', 'data/basic_portfolio_metrics.db')
        
        if Path(db_path).exists():
            # Try to connect to database
            basic_metrics_db = BasicMetricsDatabase(db_path)
            validation_checks.append(("Basic database connectivity", True))
        else:
            validation_checks.append(("Basic database exists", False))
    except Exception as e:
        validation_checks.append(("Basic database connectivity", False, str(e)))
    
    # Check if required directories exist
    required_dirs = ["data", "grafana", "logs"]
    for directory in required_dirs:
        exists = Path(directory).exists()
        validation_checks.append((f"Directory {directory}", exists))
    
    # Check if configuration files exist
    config_files = [
        "config.yaml",
        "config/metrics_config.yaml",
        "grafana/basic_dashboard_config.json"
    ]
    for config_file in config_files:
        exists = Path(config_file).exists()
        validation_checks.append((f"Config file {config_file}", exists))
    
    # Check if basic metrics are enabled in config
    try:
        config = load_config()
        basic_metrics_enabled = config.get('basic_metrics', {}).get('enable_basic_metrics', False)
        validation_checks.append(("Basic metrics enabled in config", basic_metrics_enabled))
    except Exception as e:
        validation_checks.append(("Basic metrics config check", False, str(e)))
    
    # Print validation results
    all_passed = True
    for check in validation_checks:
        if len(check) == 2:
            name, passed = check
            status = "✅" if passed else "❌"
            print(f"{status} {name}")
            if not passed:
                all_passed = False
        else:
            name, passed, error = check
            status = "✅" if passed else "❌"
            print(f"{status} {name}")
            if not passed:
                print(f"   Error: {error}")
                all_passed = False
    
    return all_passed


def main():
    """Main setup function for basic metrics"""
    print("🚀 Setting up Basic Portfolio Metrics System")
    print("=" * 50)
    
    steps = [
        ("Installing basic dependencies", install_basic_dependencies),
        ("Creating basic directories", create_basic_directories),
        ("Initializing basic database", initialize_basic_database),
        ("Setting up basic Grafana config", setup_basic_grafana_config),
        ("Validating basic setup", validate_basic_setup)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        success = step_func()
        
        if not success:
            print(f"❌ Basic setup failed at step: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 Basic Portfolio Metrics System setup completed successfully!")
    print("\nBasic metrics enabled:")
    print("✅ PnL Curve - Cumulative arithmetic returns")
    print("✅ Sharpe Ratio - Risk-adjusted returns")
    print("✅ Calmar Ratio - Return/max drawdown ratio")
    print("✅ Annualized Returns - Arithmetic annual return")
    print("✅ Max Drawdown - Peak-to-trough decline")
    print("✅ Max Time to Recovery - Days to recover from drawdown")
    print("✅ Portfolio Ranking & Exposures - Signal strength distribution")
    print("✅ Performance Attribution - Long vs short leg contribution")
    print("✅ Turnover - Portfolio turnover rate")
    print("✅ Capacity Utilization - Capital deployment efficiency")
    print("✅ Slippage - Execution cost analysis")
    
    print("\nNext steps:")
    print("1. Review basic configuration in config.yaml")
    print("2. Import basic Grafana dashboard from grafana/basic_dashboard_config.json")
    print("3. Configure Grafana data source using grafana/basic_datasource_config.json")
    print("4. Run your trading system with basic metrics enabled")
    print("5. Later, you can enable advanced metrics by switching to enhanced_metrics")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
