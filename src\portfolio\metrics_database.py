"""
Database storage and management for portfolio metrics

This module handles persistent storage of performance metrics for Grafana integration
and historical analysis.
"""

import logging
import sqlite3
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from pathlib import Path
from contextlib import contextmanager

from .metrics_calculator import PerformanceMetrics

logger = logging.getLogger(__name__)


class MetricsDatabase:
    """
    Database manager for portfolio metrics storage
    
    Handles SQLite database operations for storing and retrieving
    performance metrics for Grafana dashboard integration.
    """
    
    def __init__(self, db_path: str = "portfolio_metrics.db"):
        """
        Initialize metrics database
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
        
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database schema
        self._initialize_database()
        
        self.logger.info(f"📊 Metrics database initialized: {self.db_path}")
    
    def _initialize_database(self):
        """Create database tables if they don't exist"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Strategy performance metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    period_start DATETIME,
                    period_end DATETIME,
                    
                    -- Performance metrics
                    total_return REAL,
                    annualized_return REAL,
                    volatility REAL,
                    sharpe_ratio REAL,
                    calmar_ratio REAL,
                    
                    -- Risk metrics
                    max_drawdown REAL,
                    max_drawdown_duration_days INTEGER,
                    var_95 REAL,
                    cvar_95 REAL,
                    
                    -- Attribution metrics
                    long_return REAL,
                    short_return REAL,
                    long_sharpe REAL,
                    short_sharpe REAL,
                    
                    -- Trading metrics
                    turnover_rate REAL,
                    capacity_utilization REAL,
                    avg_slippage_bps REAL,
                    
                    -- Signal metrics
                    signal_strength_avg REAL,
                    signal_strength_std REAL,
                    position_concentration REAL,
                    
                    -- Metadata
                    calculation_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Portfolio-level metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS portfolio_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    period_start DATETIME,
                    period_end DATETIME,
                    
                    -- Performance metrics
                    total_return REAL,
                    annualized_return REAL,
                    volatility REAL,
                    sharpe_ratio REAL,
                    calmar_ratio REAL,
                    
                    -- Risk metrics
                    max_drawdown REAL,
                    max_drawdown_duration_days INTEGER,
                    var_95 REAL,
                    cvar_95 REAL,
                    
                    -- Attribution metrics
                    long_return REAL,
                    short_return REAL,
                    long_sharpe REAL,
                    short_sharpe REAL,
                    
                    -- Trading metrics
                    turnover_rate REAL,
                    capacity_utilization REAL,
                    avg_slippage_bps REAL,
                    
                    -- Portfolio composition
                    total_strategies INTEGER,
                    total_positions INTEGER,
                    long_positions INTEGER,
                    short_positions INTEGER,
                    total_capital REAL,
                    long_capital REAL,
                    short_capital REAL,
                    
                    -- Metadata
                    calculation_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Daily PnL tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_pnl (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    strategy_name TEXT,  -- NULL for aggregate portfolio
                    
                    -- Daily metrics
                    daily_return REAL,
                    cumulative_return REAL,
                    daily_pnl_usd REAL,
                    cumulative_pnl_usd REAL,
                    
                    -- Position metrics
                    positions_count INTEGER,
                    long_positions INTEGER,
                    short_positions INTEGER,
                    total_exposure_usd REAL,
                    net_exposure_usd REAL,
                    
                    -- Risk metrics
                    daily_var_95 REAL,
                    portfolio_beta REAL,
                    
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, strategy_name)
                )
            """)
            
            # Position-level tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS position_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    strategy_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    
                    -- Position details
                    side TEXT NOT NULL,  -- 'long' or 'short'
                    size_usd REAL,
                    size_native REAL,
                    weight REAL,
                    confidence REAL,
                    
                    -- Performance
                    daily_pnl_usd REAL,
                    cumulative_pnl_usd REAL,
                    
                    -- Signal strength
                    signal_value REAL,
                    signal_rank INTEGER,
                    
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, strategy_name, symbol)
                )
            """)
            
            # Execution tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS execution_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    strategy_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    
                    -- Order details
                    side TEXT NOT NULL,
                    order_type TEXT,
                    size_usd REAL,
                    expected_price REAL,
                    actual_price REAL,
                    
                    -- Execution metrics
                    slippage_bps REAL,
                    execution_time_ms REAL,
                    success BOOLEAN,
                    
                    -- Market conditions
                    spread_bps REAL,
                    market_impact_bps REAL,
                    
                    order_id TEXT,
                    exchange TEXT
                )
            """)
            
            # Create indexes for better query performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategy_metrics_name_time ON strategy_metrics(strategy_name, timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_time ON portfolio_metrics(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_daily_pnl_date_strategy ON daily_pnl(date, strategy_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_position_tracking_date_strategy ON position_tracking(date, strategy_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_execution_tracking_time_strategy ON execution_tracking(timestamp, strategy_name)")
            
            conn.commit()
            self.logger.info("📊 Database schema initialized successfully")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper error handling"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def store_strategy_metrics(self, strategy_name: str, metrics: PerformanceMetrics) -> bool:
        """
        Store strategy performance metrics
        
        Args:
            strategy_name: Name of the strategy
            metrics: PerformanceMetrics object
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO strategy_metrics (
                        strategy_name, timestamp, period_start, period_end,
                        total_return, annualized_return, volatility, sharpe_ratio, calmar_ratio,
                        max_drawdown, max_drawdown_duration_days, var_95, cvar_95,
                        long_return, short_return, long_sharpe, short_sharpe,
                        turnover_rate, capacity_utilization, avg_slippage_bps,
                        signal_strength_avg, signal_strength_std, position_concentration
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    strategy_name,
                    metrics.calculation_timestamp,
                    metrics.period_start,
                    metrics.period_end,
                    metrics.total_return,
                    metrics.annualized_return,
                    metrics.volatility,
                    metrics.sharpe_ratio,
                    metrics.calmar_ratio,
                    metrics.max_drawdown,
                    metrics.max_drawdown_duration_days,
                    metrics.var_95,
                    metrics.cvar_95,
                    metrics.long_return,
                    metrics.short_return,
                    metrics.long_sharpe,
                    metrics.short_sharpe,
                    metrics.turnover_rate,
                    metrics.capacity_utilization,
                    metrics.avg_slippage_bps,
                    metrics.signal_strength_avg,
                    metrics.signal_strength_std,
                    metrics.position_concentration
                ))
                
                conn.commit()
                self.logger.debug(f"📊 Stored metrics for strategy: {strategy_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to store strategy metrics for {strategy_name}: {e}")
            return False

    def store_portfolio_metrics(self, metrics: PerformanceMetrics,
                              portfolio_stats: Dict[str, Any]) -> bool:
        """
        Store aggregate portfolio metrics

        Args:
            metrics: PerformanceMetrics object for portfolio
            portfolio_stats: Additional portfolio statistics

        Returns:
            True if successful, False otherwise
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO portfolio_metrics (
                        timestamp, period_start, period_end,
                        total_return, annualized_return, volatility, sharpe_ratio, calmar_ratio,
                        max_drawdown, max_drawdown_duration_days, var_95, cvar_95,
                        long_return, short_return, long_sharpe, short_sharpe,
                        turnover_rate, capacity_utilization, avg_slippage_bps,
                        total_strategies, total_positions, long_positions, short_positions,
                        total_capital, long_capital, short_capital
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    metrics.calculation_timestamp,
                    metrics.period_start,
                    metrics.period_end,
                    metrics.total_return,
                    metrics.annualized_return,
                    metrics.volatility,
                    metrics.sharpe_ratio,
                    metrics.calmar_ratio,
                    metrics.max_drawdown,
                    metrics.max_drawdown_duration_days,
                    metrics.var_95,
                    metrics.cvar_95,
                    metrics.long_return,
                    metrics.short_return,
                    metrics.long_sharpe,
                    metrics.short_sharpe,
                    metrics.turnover_rate,
                    metrics.capacity_utilization,
                    metrics.avg_slippage_bps,
                    portfolio_stats.get('total_strategies', 0),
                    portfolio_stats.get('total_positions', 0),
                    portfolio_stats.get('long_positions', 0),
                    portfolio_stats.get('short_positions', 0),
                    portfolio_stats.get('total_capital', 0),
                    portfolio_stats.get('long_capital', 0),
                    portfolio_stats.get('short_capital', 0)
                ))

                conn.commit()
                self.logger.debug("📊 Stored portfolio metrics")
                return True

        except Exception as e:
            self.logger.error(f"Failed to store portfolio metrics: {e}")
            return False

    def store_daily_pnl(self, date: datetime, strategy_name: Optional[str],
                       pnl_data: Dict[str, Any]) -> bool:
        """
        Store daily PnL data

        Args:
            date: Date for the PnL data
            strategy_name: Strategy name (None for aggregate)
            pnl_data: Dictionary containing PnL metrics

        Returns:
            True if successful, False otherwise
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT OR REPLACE INTO daily_pnl (
                        date, strategy_name, daily_return, cumulative_return,
                        daily_pnl_usd, cumulative_pnl_usd, positions_count,
                        long_positions, short_positions, total_exposure_usd,
                        net_exposure_usd, daily_var_95, portfolio_beta
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    date.date(),
                    strategy_name,
                    pnl_data.get('daily_return', 0),
                    pnl_data.get('cumulative_return', 0),
                    pnl_data.get('daily_pnl_usd', 0),
                    pnl_data.get('cumulative_pnl_usd', 0),
                    pnl_data.get('positions_count', 0),
                    pnl_data.get('long_positions', 0),
                    pnl_data.get('short_positions', 0),
                    pnl_data.get('total_exposure_usd', 0),
                    pnl_data.get('net_exposure_usd', 0),
                    pnl_data.get('daily_var_95', 0),
                    pnl_data.get('portfolio_beta', 0)
                ))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"Failed to store daily PnL: {e}")
            return False

    def get_strategy_metrics_history(self, strategy_name: str,
                                   days: int = 30) -> List[Dict[str, Any]]:
        """
        Get historical metrics for a strategy

        Args:
            strategy_name: Name of the strategy
            days: Number of days to retrieve

        Returns:
            List of metric records
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM strategy_metrics
                    WHERE strategy_name = ?
                    AND timestamp >= datetime('now', '-{} days')
                    ORDER BY timestamp DESC
                """.format(days), (strategy_name,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Failed to get strategy metrics history: {e}")
            return []

    def get_portfolio_metrics_history(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        Get historical portfolio metrics

        Args:
            days: Number of days to retrieve

        Returns:
            List of metric records
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM portfolio_metrics
                    WHERE timestamp >= datetime('now', '-{} days')
                    ORDER BY timestamp DESC
                """.format(days))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Failed to get portfolio metrics history: {e}")
            return []

    def get_daily_pnl_series(self, strategy_name: Optional[str] = None,
                           days: int = 30) -> List[Dict[str, Any]]:
        """
        Get daily PnL time series

        Args:
            strategy_name: Strategy name (None for aggregate)
            days: Number of days to retrieve

        Returns:
            List of daily PnL records
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                if strategy_name:
                    cursor.execute("""
                        SELECT * FROM daily_pnl
                        WHERE strategy_name = ?
                        AND date >= date('now', '-{} days')
                        ORDER BY date ASC
                    """.format(days), (strategy_name,))
                else:
                    cursor.execute("""
                        SELECT * FROM daily_pnl
                        WHERE strategy_name IS NULL
                        AND date >= date('now', '-{} days')
                        ORDER BY date ASC
                    """.format(days))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"Failed to get daily PnL series: {e}")
            return []
