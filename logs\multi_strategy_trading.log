2025-06-16 13:46:09,209 - utils.logging - INFO - 🔧 Logging configured: level=20, file=logs\multi_strategy_trading.log, debug=False
2025-06-16 13:46:09,210 - __main__ - INFO - 🔧 Starting Multi-Strategy Trading System with graceful shutdown support
2025-06-16 13:46:09,210 - __main__ - INFO - 🔑 Handling credential setup...
2025-06-16 13:46:09,222 - storage.secure_storage - INFO - 📁 Credentials loaded from credentials.json
2025-06-16 13:46:09,223 - __main__ - INFO - 🔑 Loaded stored credentials
2025-06-16 13:46:09,224 - config.validation - INFO - 🔧 Validating configuration parameters...
2025-06-16 13:46:09,225 - config.validation - WARNING - ⚠️ 'circuit_breaker_threshold' is deprecated. Error handling is now managed by ErrorRecoveryManager.
2025-06-16 13:46:09,225 - config.validation - INFO - 🧹 Removed deprecated field: circuit_breaker_threshold
2025-06-16 13:46:09,226 - config.validation - WARNING - ⚠️ 'circuit_breaker_timeout' is deprecated. Error handling is now managed by ErrorRecoveryManager.
2025-06-16 13:46:09,226 - config.validation - INFO - 🧹 Removed deprecated field: circuit_breaker_timeout
2025-06-16 13:46:09,226 - config.validation - INFO - ✅ Configuration validation passed
2025-06-16 13:46:09,227 - __main__ - INFO - Configuration: Bybit Demo, $10,000, 3 strategies enabled, simulation=False
2025-06-16 13:46:09,227 - __main__ - INFO - Enabled strategies: ['stat_arb_carry_trade', 'cross_sectional_momentum', 'trend_trading']
2025-06-16 13:46:09,231 - storage.state_manager - INFO - 📁 No existing state file found - starting fresh
2025-06-16 13:46:09,231 - strategies.strategy_manager - INFO - 🏗️ Strategy manager initialized with 3 configured strategies
2025-06-16 13:46:09,232 - portfolio.combiner - INFO - 🏗️ Portfolio combiner initialized
2025-06-16 13:46:09,233 - portfolio.combiner - INFO -    Min position size: $10.0
2025-06-16 13:46:09,233 - portfolio.combiner - INFO -    Position netting: enabled
2025-06-16 13:46:09,234 - portfolio.performance_tracker - INFO - 🏗️ Performance tracker initialized
2025-06-16 13:46:09,235 - portfolio.performance_tracker - INFO -    Max history: 90 days
2025-06-16 13:46:09,235 - portfolio.performance_tracker - INFO -    Detailed tracking: enabled
2025-06-16 13:46:09,235 - portfolio.performance_tracker - INFO - 📂 No existing performance history found, starting fresh
2025-06-16 13:46:09,237 - core.orchestrator - INFO - 🏗️ Multi-strategy orchestrator initialized
2025-06-16 13:46:09,237 - __main__ - INFO - 🔌 Initializing bybit exchange connection and orchestrator...
2025-06-16 13:46:09,237 - exchanges.bybit - INFO - 🔑 Using API keys for private endpoints
2025-06-16 13:46:09,248 - exchanges.bybit - INFO - 🎭 Bybit demo trading mode enabled via CCXT
2025-06-16 13:46:29,891 - exchanges.bybit - INFO - ✅ Connected to Bybit demo
2025-06-16 13:46:29,891 - exchanges.bybit - INFO - ✅ Found 2626 markets
2025-06-16 13:46:32,421 - exchanges.bybit - INFO - ✅ Public API test successful with BTC/USDT
2025-06-16 13:46:35,002 - exchanges.bybit - INFO - ✅ API keys are valid and working
2025-06-16 13:46:35,003 - exchanges.bybit - INFO - ✅ Demo account balance available: 8 currencies
2025-06-16 13:46:35,004 - core.orchestrator - INFO - ✅ Successfully initialized bybit exchange
2025-06-16 13:46:35,005 - data.analyzer - INFO - 🔧 Beta projection disabled
2025-06-16 13:46:35,007 - execution.randomized_executor - INFO - 🐌 Conservative execution style applied - prioritizing API safety over speed
2025-06-16 13:46:35,007 - execution.randomized_executor - INFO - 🏗️ Randomized executor initialized with enhanced error recovery
2025-06-16 13:46:35,008 - execution.randomized_executor - INFO -    Execution style: conservative
2025-06-16 13:46:35,009 - execution.randomized_executor - INFO -    Max concurrent batches: 6
2025-06-16 13:46:35,011 - execution.randomized_executor - INFO -    Emergency throttling: enabled
2025-06-16 13:46:35,011 - execution.randomized_executor - INFO -    Async batch execution: disabled
2025-06-16 13:46:35,012 - core.orchestrator - INFO - ✅ Initialized shared data fetcher, analyzer, and execution modules
2025-06-16 13:46:35,013 - core.orchestrator - INFO - 🔧 Initializing 3 configured strategies...
2025-06-16 13:46:35,031 - strategies.stat_arb_carry_trade.strategy - INFO - 📂 Loaded strategy config from C:\Users\<USER>\OneDrive - Manipal University College Malaysia\Desktop\funding_arb\src\strategies\stat_arb_carry_trade\config.yaml
2025-06-16 13:46:35,032 - core.orchestrator - ERROR - ❌ Failed to initialize strategy stat_arb_carry_trade: Missing required strategy parameter: peak_abs_funding_rate
2025-06-16 13:46:35,045 - strategies.cross_sectional_momentum.strategy - INFO - 📁 Loaded strategy config from C:\Users\<USER>\OneDrive - Manipal University College Malaysia\Desktop\funding_arb\src\strategies\cross_sectional_momentum\config.yaml
2025-06-16 13:46:35,046 - strategies.base.cross_sectional_momentum - INFO - ✅ Strategy configuration validated successfully
2025-06-16 13:46:35,046 - strategies.base.cross_sectional_momentum - INFO - 🏗️ Initialized strategy: cross_sectional_momentum
2025-06-16 13:46:35,046 - strategies.base.cross_sectional_momentum - INFO - 🏗️ Cross-Sectional Momentum strategy initialized
2025-06-16 13:46:35,047 - strategies.base.cross_sectional_momentum - INFO -    Total capital: $10,000
2025-06-16 13:46:35,047 - strategies.base.cross_sectional_momentum - INFO -    Exchange: bybit
2025-06-16 13:46:35,048 - strategies.base.cross_sectional_momentum - INFO -    Target positions per leg: 5
2025-06-16 13:46:35,048 - strategies.base.cross_sectional_momentum - INFO -    Target volatility: 20.0%
2025-06-16 13:46:35,048 - strategies.strategy_manager - INFO - ✅ Registered strategy: cross_sectional_momentum (weight: 0.33)
2025-06-16 13:46:35,049 - core.orchestrator - INFO - ✅ Initialized and registered strategy: cross_sectional_momentum (weight: 0.33)
2025-06-16 13:46:35,050 - core.orchestrator - ERROR - ❌ Failed to initialize strategy trend_trading: Missing required config parameter: top_market_cap_count
2025-06-16 13:46:35,050 - strategies.strategy_manager - INFO - ✅ All 1 strategies validated successfully
2025-06-16 13:46:35,050 - core.orchestrator - INFO - ✅ Successfully initialized 1 strategies: ['cross_sectional_momentum']
2025-06-16 13:46:35,050 - core.orchestrator - INFO - 📁 Loaded state: last_rebalance: Never, last_monitoring: Never, positions: 0, orders: 0
2025-06-16 13:46:35,051 - __main__ - INFO - 🚀 Starting orchestrator execution with shutdown monitoring...
2025-06-16 13:46:35,051 - core.orchestrator - INFO - 🚀 Starting multi-strategy orchestrator execution loop
2025-06-16 13:46:35,051 - core.orchestrator - INFO - 🚀 immediate_start=True: No previous rebalance - starting now
2025-06-16 13:46:35,052 - core.orchestrator - INFO - ⚖️ Starting multi-strategy rebalancing process...
2025-06-16 13:46:35,052 - core.orchestrator - INFO - 🔄 Starting multi-strategy portfolio rebalancing...
2025-06-16 13:46:35,052 - core.orchestrator - INFO - 📡 Pre-fetching market data for all strategies...
2025-06-16 13:46:35,053 - data.fetcher - INFO - 📡 Fetching markets and tickers concurrently...
2025-06-16 13:46:36,020 - utils.contract_specs - INFO - 📋 Cached market info for 2626 symbols
2025-06-16 13:46:36,022 - data.fetcher - INFO - 📈 Found 528 USDT-margined perpetual futures contracts
2025-06-16 13:46:36,023 - data.fetcher - INFO - 📊 Processing batch 1/18 (30 symbols)
2025-06-16 13:46:37,264 - utils.shutdown - INFO - 🛑 Received SIGINT signal - initiating graceful shutdown...
2025-06-16 13:46:37,264 - __main__ - INFO - 🛑 Shutdown signal received - executing graceful shutdown...
2025-06-16 13:46:37,265 - utils.shutdown - INFO - 🛑 Starting graceful shutdown sequence...
2025-06-16 13:46:37,265 - utils.shutdown - INFO - 🛑 Stopping strategy execution...
2025-06-16 13:46:37,265 - core.orchestrator - INFO - 🛑 Stopping multi-strategy orchestrator...
2025-06-16 13:46:37,266 - utils.shutdown - INFO - ✅ Strategy stopped
2025-06-16 13:46:37,267 - utils.shutdown - INFO - 🚫 Canceling all open orders...
2025-06-16 13:46:37,268 - execution.randomized_executor - INFO - 🚫 Canceling all open orders...
2025-06-16 13:46:37,268 - execution.randomized_executor - INFO - 📡 Fetching current positions from exchange...
2025-06-16 13:46:37,269 - core.orchestrator - INFO - 🛑 Shutdown requested - skipping orchestrator cleanup (handled by shutdown coordinator)
2025-06-16 13:46:51,448 - execution.randomized_executor - INFO - 📊 Found 0 active positions
2025-06-16 13:46:51,449 - execution.randomized_executor - INFO - 📋 No positions found, assuming no open orders
2025-06-16 13:46:51,450 - execution.randomized_executor - INFO - 📋 No open orders to cancel
2025-06-16 13:46:51,450 - utils.shutdown - INFO - ✅ All orders canceled
2025-06-16 13:46:51,451 - utils.shutdown - INFO - 🚫 Canceling 1 running tasks...
2025-06-16 13:46:51,452 - utils.shutdown - INFO - ✅ All tasks canceled
2025-06-16 13:46:51,453 - utils.shutdown - INFO - 💾 Saving final state...
2025-06-16 13:46:51,465 - utils.shutdown - INFO - ✅ Final state saved
2025-06-16 13:46:51,466 - utils.shutdown - INFO - 🔌 Closing exchange connections...
2025-06-16 13:46:51,466 - utils.shutdown - INFO - ✅ Exchange connections closed
2025-06-16 13:46:51,466 - utils.shutdown - INFO - ✅ Graceful shutdown completed successfully in 14.2s
2025-06-16 13:46:51,467 - __main__ - INFO - ✅ Graceful shutdown completed successfully
2025-06-16 13:49:27,115 - utils.logging - INFO - 🔧 Logging configured: level=20, file=logs\multi_strategy_trading.log, debug=False
2025-06-16 13:49:27,116 - __main__ - INFO - 🔧 Starting Multi-Strategy Trading System with graceful shutdown support
2025-06-16 13:49:27,117 - __main__ - INFO - 🔑 Handling credential setup...
2025-06-16 13:49:27,117 - storage.secure_storage - INFO - 📁 Credentials loaded from credentials.json
2025-06-16 13:49:27,117 - __main__ - INFO - 🔑 Loaded stored credentials
2025-06-16 13:49:27,118 - config.validation - INFO - 🔧 Validating configuration parameters...
2025-06-16 13:49:27,118 - config.validation - WARNING - ⚠️ 'circuit_breaker_threshold' is deprecated. Error handling is now managed by ErrorRecoveryManager.
2025-06-16 13:49:27,118 - config.validation - INFO - 🧹 Removed deprecated field: circuit_breaker_threshold
2025-06-16 13:49:27,119 - config.validation - WARNING - ⚠️ 'circuit_breaker_timeout' is deprecated. Error handling is now managed by ErrorRecoveryManager.
2025-06-16 13:49:27,119 - config.validation - INFO - 🧹 Removed deprecated field: circuit_breaker_timeout
2025-06-16 13:49:27,120 - config.validation - INFO - ✅ Configuration validation passed
2025-06-16 13:49:27,120 - __main__ - INFO - Configuration: Bybit Demo, $10,000, 3 strategies enabled, simulation=False
2025-06-16 13:49:27,120 - __main__ - INFO - Enabled strategies: ['stat_arb_carry_trade', 'cross_sectional_momentum', 'trend_trading']
2025-06-16 13:49:27,121 - storage.state_manager - INFO - 📁 Loaded state: 0 positions, 0 pending orders
2025-06-16 13:49:27,121 - strategies.strategy_manager - INFO - 🏗️ Strategy manager initialized with 3 configured strategies
2025-06-16 13:49:27,122 - portfolio.combiner - INFO - 🏗️ Portfolio combiner initialized
2025-06-16 13:49:27,122 - portfolio.combiner - INFO -    Min position size: $10.0
2025-06-16 13:49:27,123 - portfolio.combiner - INFO -    Position netting: enabled
2025-06-16 13:49:27,123 - portfolio.performance_tracker - INFO - 🏗️ Performance tracker initialized
2025-06-16 13:49:27,123 - portfolio.performance_tracker - INFO -    Max history: 90 days
2025-06-16 13:49:27,124 - portfolio.performance_tracker - INFO -    Detailed tracking: enabled
2025-06-16 13:49:27,124 - portfolio.performance_tracker - INFO - 📂 No existing performance history found, starting fresh
2025-06-16 13:49:27,124 - core.orchestrator - INFO - 🏗️ Multi-strategy orchestrator initialized
2025-06-16 13:49:27,125 - __main__ - INFO - 🔌 Initializing bybit exchange connection and orchestrator...
2025-06-16 13:49:27,125 - exchanges.bybit - INFO - 🔑 Using API keys for private endpoints
2025-06-16 13:49:27,131 - exchanges.bybit - INFO - 🎭 Bybit demo trading mode enabled via CCXT
2025-06-16 13:49:47,707 - exchanges.bybit - INFO - ✅ Connected to Bybit demo
2025-06-16 13:49:47,707 - exchanges.bybit - INFO - ✅ Found 2626 markets
2025-06-16 13:49:50,235 - exchanges.bybit - INFO - ✅ Public API test successful with BTC/USDT
2025-06-16 13:49:52,771 - exchanges.bybit - INFO - ✅ API keys are valid and working
2025-06-16 13:49:52,772 - exchanges.bybit - INFO - ✅ Demo account balance available: 8 currencies
2025-06-16 13:49:52,772 - core.orchestrator - INFO - ✅ Successfully initialized bybit exchange
2025-06-16 13:49:52,773 - data.analyzer - INFO - 🔧 Beta projection disabled
2025-06-16 13:49:52,774 - execution.randomized_executor - INFO - 🐌 Conservative execution style applied - prioritizing API safety over speed
2025-06-16 13:49:52,774 - execution.randomized_executor - INFO - 🏗️ Randomized executor initialized with enhanced error recovery
2025-06-16 13:49:52,775 - execution.randomized_executor - INFO -    Execution style: conservative
2025-06-16 13:49:52,776 - execution.randomized_executor - INFO -    Max concurrent batches: 6
2025-06-16 13:49:52,777 - execution.randomized_executor - INFO -    Emergency throttling: enabled
2025-06-16 13:49:52,777 - execution.randomized_executor - INFO -    Async batch execution: disabled
2025-06-16 13:49:52,779 - core.orchestrator - INFO - ✅ Initialized shared data fetcher, analyzer, and execution modules
2025-06-16 13:49:52,779 - core.orchestrator - INFO - 🔧 Initializing 3 configured strategies...
2025-06-16 13:49:52,799 - strategies.stat_arb_carry_trade.strategy - INFO - 📂 Loaded strategy config from C:\Users\<USER>\OneDrive - Manipal University College Malaysia\Desktop\funding_arb\src\strategies\stat_arb_carry_trade\config.yaml
2025-06-16 13:49:52,799 - strategies.base.stat_arb_carry_trade - INFO - ✅ Strategy configuration validated
2025-06-16 13:49:52,800 - strategies.base.stat_arb_carry_trade - INFO - 🏗️ Initialized strategy: stat_arb_carry_trade
2025-06-16 13:49:52,800 - strategies.base.stat_arb_carry_trade - INFO - 🏗️ StatArb Carry Trade strategy initialized
2025-06-16 13:49:52,801 - strategies.base.stat_arb_carry_trade - INFO -    Total capital: $10,000
2025-06-16 13:49:52,801 - strategies.base.stat_arb_carry_trade - INFO -    Exchange: bybit
2025-06-16 13:49:52,801 - strategies.strategy_manager - INFO - ✅ Registered strategy: stat_arb_carry_trade (weight: 0.33)
2025-06-16 13:49:52,802 - core.orchestrator - INFO - ✅ Initialized and registered strategy: stat_arb_carry_trade (weight: 0.33)
2025-06-16 13:49:52,807 - strategies.cross_sectional_momentum.strategy - INFO - 📁 Loaded strategy config from C:\Users\<USER>\OneDrive - Manipal University College Malaysia\Desktop\funding_arb\src\strategies\cross_sectional_momentum\config.yaml
2025-06-16 13:49:52,807 - strategies.base.cross_sectional_momentum - INFO - ✅ Strategy configuration validated successfully
2025-06-16 13:49:52,807 - strategies.base.cross_sectional_momentum - INFO - 🏗️ Initialized strategy: cross_sectional_momentum
2025-06-16 13:49:52,808 - strategies.base.cross_sectional_momentum - INFO - 🏗️ Cross-Sectional Momentum strategy initialized
2025-06-16 13:49:52,808 - strategies.base.cross_sectional_momentum - INFO -    Total capital: $10,000
2025-06-16 13:49:52,808 - strategies.base.cross_sectional_momentum - INFO -    Exchange: bybit
2025-06-16 13:49:52,809 - strategies.base.cross_sectional_momentum - INFO -    Target positions per leg: 5
2025-06-16 13:49:52,809 - strategies.base.cross_sectional_momentum - INFO -    Target volatility: 20.0%
2025-06-16 13:49:52,810 - strategies.strategy_manager - INFO - ✅ Registered strategy: cross_sectional_momentum (weight: 0.33)
2025-06-16 13:49:52,810 - core.orchestrator - INFO - ✅ Initialized and registered strategy: cross_sectional_momentum (weight: 0.33)
2025-06-16 13:49:52,810 - core.orchestrator - ERROR - ❌ Failed to initialize strategy trend_trading: Missing required config parameter: top_market_cap_count
2025-06-16 13:49:52,811 - strategies.strategy_manager - INFO - ✅ All 2 strategies validated successfully
2025-06-16 13:49:52,811 - core.orchestrator - INFO - ✅ Successfully initialized 2 strategies: ['stat_arb_carry_trade', 'cross_sectional_momentum']
2025-06-16 13:49:52,811 - core.orchestrator - INFO - 📁 Loaded state: last_rebalance: Never, last_monitoring: Never, positions: 0, orders: 0
2025-06-16 13:49:52,811 - __main__ - INFO - 🚀 Starting orchestrator execution with shutdown monitoring...
2025-06-16 13:49:52,812 - core.orchestrator - INFO - 🚀 Starting multi-strategy orchestrator execution loop
2025-06-16 13:49:52,812 - core.orchestrator - INFO - 🚀 immediate_start=True: No previous rebalance - starting now
2025-06-16 13:49:52,812 - core.orchestrator - INFO - ⚖️ Starting multi-strategy rebalancing process...
2025-06-16 13:49:52,812 - core.orchestrator - INFO - 🔄 Starting multi-strategy portfolio rebalancing...
2025-06-16 13:49:52,813 - core.orchestrator - INFO - 📡 Pre-fetching market data for all strategies...
2025-06-16 13:49:52,813 - data.fetcher - INFO - 📡 Fetching markets and tickers concurrently...
2025-06-16 13:49:53,746 - utils.contract_specs - INFO - 📋 Cached market info for 2626 symbols
2025-06-16 13:49:53,747 - data.fetcher - INFO - 📈 Found 528 USDT-margined perpetual futures contracts
2025-06-16 13:49:53,748 - data.fetcher - INFO - 📊 Processing batch 1/18 (30 symbols)
2025-06-16 13:54:54,080 - data.fetcher - INFO - ✅ Batch 1 completed in 300.33s: 0 eligible coins found
2025-06-16 13:54:55,082 - data.fetcher - INFO - 📊 Processing batch 2/18 (30 symbols)
2025-06-16 13:59:45,430 - data.fetcher - INFO - ✅ Batch 2 completed in 290.35s: 0 eligible coins found
2025-06-16 13:59:46,433 - data.fetcher - INFO - 📊 Processing batch 3/18 (30 symbols)
2025-06-16 13:59:51,421 - utils.shutdown - INFO - 🛑 Received SIGINT signal - initiating graceful shutdown...
2025-06-16 13:59:51,422 - __main__ - INFO - 🛑 Shutdown signal received - executing graceful shutdown...
2025-06-16 13:59:51,422 - utils.shutdown - INFO - 🛑 Starting graceful shutdown sequence...
2025-06-16 13:59:51,423 - utils.shutdown - INFO - 🛑 Stopping strategy execution...
2025-06-16 13:59:51,423 - core.orchestrator - INFO - 🛑 Stopping multi-strategy orchestrator...
2025-06-16 13:59:51,424 - utils.shutdown - INFO - ✅ Strategy stopped
2025-06-16 13:59:51,425 - utils.shutdown - INFO - 🚫 Canceling all open orders...
2025-06-16 13:59:51,426 - execution.randomized_executor - INFO - 🚫 Canceling all open orders...
2025-06-16 13:59:51,426 - execution.randomized_executor - INFO - 📡 Fetching current positions from exchange...
2025-06-16 13:59:51,429 - core.orchestrator - INFO - 🛑 Shutdown requested - skipping orchestrator cleanup (handled by shutdown coordinator)
2025-06-16 14:00:06,797 - execution.randomized_executor - INFO - 📊 Found 0 active positions
2025-06-16 14:00:06,798 - execution.randomized_executor - INFO - 📋 No positions found, assuming no open orders
2025-06-16 14:00:06,798 - execution.randomized_executor - INFO - 📋 No open orders to cancel
2025-06-16 14:00:06,798 - utils.shutdown - INFO - ✅ All orders canceled
2025-06-16 14:00:06,799 - utils.shutdown - INFO - 🚫 Canceling 1 running tasks...
2025-06-16 14:00:06,799 - utils.shutdown - INFO - ✅ All tasks canceled
2025-06-16 14:00:06,799 - utils.shutdown - INFO - 💾 Saving final state...
2025-06-16 14:00:06,813 - utils.shutdown - INFO - ✅ Final state saved
2025-06-16 14:00:06,813 - utils.shutdown - INFO - 🔌 Closing exchange connections...
2025-06-16 14:00:06,814 - utils.shutdown - INFO - ✅ Exchange connections closed
2025-06-16 14:00:06,814 - utils.shutdown - INFO - ✅ Graceful shutdown completed successfully in 15.4s
2025-06-16 14:00:06,814 - __main__ - INFO - ✅ Graceful shutdown completed successfully
